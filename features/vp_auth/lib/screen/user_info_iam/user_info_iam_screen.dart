import 'package:flutter/material.dart';
import 'package:vp_auth/cubit/user_info_iam/user_info_iam_cubit.dart';
import 'package:vp_core/vp_core.dart';

class UserInfoIamScreen extends StatefulWidget {
  const UserInfoIamScreen({super.key});

  @override
  State<UserInfoIamScreen> createState() => _UserInfoIamScreenState();
}

class _UserInfoIamScreenState extends State<UserInfoIamScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider<UserInfoIamCubit>(
      create: (context) => UserInfoIamCubit()..getCustomerInfo(),
      child: BlocListener<UserInfoIamCubit, UserInfoIamState>(
        listener: (context, state) {
          if (state.customerInfo != null) {
            context.pop(state.customerInfo);
          } else if (!state.isLoading && state.errorMessage != null) {
            context.pop(null);
          } else if (!state.isLoading && state.customerInfo == null) {
            context.pop(null);
          }
        },
        child: const Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(child: SizedBox()),
        ),
      ),
    );
  }
}
