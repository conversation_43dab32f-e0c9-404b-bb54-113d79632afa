import 'dart:math';

import 'package:flutter/material.dart';

import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/tabs/stock_capital_dividend_tab/stock_capital_dividend_tab.dart';
import 'package:stock_detail/tabs/stock_cashflow_tab/stock_cash_flow_tab.dart';
import 'package:stock_detail/tabs/stock_cw_info_tab/stock_cw_info_tab.dart';
import 'package:stock_detail/tabs/stock_event_tab/stock_events_tab.dart';
import 'package:stock_detail/tabs/stock_financial_tab/stock_financial_tab.dart';
import 'package:stock_detail/tabs/stock_index_tab/stock_index_tab.dart';
import 'package:stock_detail/tabs/stock_news_tab/etf/stock_etf_news_tab.dart';
import 'package:stock_detail/tabs/stock_news_tab/stock_news_tab.dart';
import 'package:stock_detail/tabs/stock_profile_tab/stock_profile_tab.dart';
import 'package:stock_detail/tabs/stock_rating_tab/stock_rating_tab.dart';
import 'package:stock_detail/tabs/stock_statistics_tab/stock_statistics_tab.dart';
import 'package:stock_detail/tabs/stock_trading_tab/stock_trading_tab.dart';
import 'package:stock_detail/widgets/tagging/tagging_view.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'widgets/stock_detail_buy_sell_view.dart';

class CustomTab extends StatelessWidget {
  const CustomTab({required this.text, super.key});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: Tab(text: text),
    );
  }
}

class StockDetailContentView extends StatefulWidget {
  const StockDetailContentView({
    required this.stock,
    this.initialTab,
    super.key,
  });

  final StockDetailTab? initialTab;

  final StockInfoModel stock;

  @override
  State<StockDetailContentView> createState() => _StockDetailContentViewState();
}

class _StockDetailContentViewState extends State<StockDetailContentView>
    with SingleTickerProviderStateMixin {
  late TabController tabController = TabController(
    initialIndex: initialIndex,
    length: tabs.$1.length,
    vsync: this,
  );

  StockInfoModel get stock => widget.stock;

  late int initialIndex =
      widget.initialTab != null
          ? max(tabs.$1.indexOf(widget.initialTab!), 0)
          : 0;

  late (List<StockDetailTab>, List<Widget>) tabs = _tabs;

  (List<StockDetailTab>, List<Widget>) get _tabs {
    if (stock.stockType == StockType.EF) {
      return (
        const [
          StockDetailTab.trading,
          StockDetailTab.news,
          StockDetailTab.profile,
          StockDetailTab.statistics,
        ],
        [
          StockTradingTabView(stockInfoModel: stock),
          StockEtfNewsTab(symbol: stock.symbol),
          StockProfileTab(symbol: stock.symbol),
          StockStatisticsTab(symbol: stock.symbol),
        ],
      );
    }

    if (stock.stockType == StockType.CW) {
      return (
        const [
          StockDetailTab.trading,
          StockDetailTab.cashFlow,
          StockDetailTab.information,
          StockDetailTab.news,
          StockDetailTab.events,
        ],
        [
          StockTradingTabView(stockInfoModel: stock),
          StockCashFlowTab(symbol: stock.symbol),
          StockCwInfoTab(symbol: stock.symbol),
          StockNewsTab(symbol: stock.symbol),
          StockEventsTab(symbol: stock.symbol),
        ],
      );
    }

    return (
      [
        StockDetailTab.trading,
        StockDetailTab.cashFlow,
        StockDetailTab.news,
        StockDetailTab.profile,
        StockDetailTab.events,
        StockDetailTab.capitalDividend,
        StockDetailTab.stockIndex,
        StockDetailTab.financial,
        StockDetailTab.statistics,
        StockDetailTab.rating,
      ],
      [
        StockTradingTabView(stockInfoModel: stock),
        StockCashFlowTab(symbol: stock.symbol),
        StockNewsTab(symbol: stock.symbol),
        StockProfileTab(symbol: stock.symbol),
        StockEventsTab(symbol: stock.symbol),
        StockCapitalDividendTab(symbol: stock.symbol),
        StockIndexTab(symbol: stock.symbol),
        StockFinancialTab(symbol: stock.symbol),
        StockStatisticsTab(symbol: stock.symbol),
        StockRatingTab(symbol: stock.symbol),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Column(
        children: [
          StockDetailPriceView(item: stock),

          VPTabBar(
            isScrollable: true,
            controller: tabController,
            padding: EdgeInsets.zero,
            tabAlignment: TabAlignment.start,
            labelPadding: EdgeInsets.zero,
            tabs:
                tabs.$1
                    .map(
                      (e) => StockDetailTabView(
                        tab: e,
                        onTap: (_, tab) => showRequiredSignInDialog(tab: tab),
                      ),
                    )
                    .toList(),
          ),

          const SizedBox(height: 8),

          Expanded(
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              controller: tabController,
              children: tabs.$2,
            ),
          ),

          StockDetailBuySellButtonView(symbol: stock.symbol),
        ],
      ),
    );
  }

  void showRequiredSignInDialog({required StockDetailTab tab}) {
    stockDetailNavigator.openSignInRequiredDialog(
      context,
      onSignInSuccess:
          () => stockDetailNavigator.openStockDetailPage(
            getContext,
            args: StockDetailArgs(symbol: stock.symbol, initialTab: tab),
          ),
    );
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }
}

class StockDetailTabView extends StatelessWidget {
  const StockDetailTabView({required this.tab, this.onTap, super.key});

  final StockDetailTab tab;

  final void Function(BuildContext, StockDetailTab)? onTap;

  @override
  Widget build(BuildContext context) {
    if (!tab.requiredSignIn) return CustomTab(text: tab.nameUI);

    return BlocSelector<AuthCubit, AuthState, bool>(
      selector: (state) => state.isLoggedIn,
      builder: (context, isLoggedIn) {
        if (isLoggedIn) {
          return CustomTab(text: tab.nameUI);
        }

        return InkWell(
          onTap: () => onTap?.call(context, tab),
          child: CustomTab(text: tab.nameUI),
        );
      },
    );
  }
}

class StockDetailPriceView extends StatelessWidget {
  const StockDetailPriceView({required this.item, super.key});

  final StockInfoModel item;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TaggingView(symbol: item.symbol),

          const SizedBox(height: 8),

          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  VPClosePriceItemView.stock(stock: item),

                  VPProfitItemView(stock: item),
                ],
              ),

              const Spacer(),

              VPTotalTradingVolumeItemView(
                symbol: item.symbol,
                initTotalVolume: item.totalTradingVolume,
                builder: (value) {
                  return _ItemView(
                    title: 'KLGD',
                    content: FormatUtils.formatVolWithTrailing(value ?? 0),
                  );
                },
              ),

              const SizedBox(width: 8),

              VPTotalTradingValueItemView(
                symbol: item.symbol,
                initTotalValue: item.totalTradingValue,
                builder: (value) {
                  return _ItemView(
                    title: 'GTGD',
                    content: FormatUtils.formatVolWithTrailing(value ?? 0),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ItemView extends StatelessWidget {
  const _ItemView({required this.title, required this.content});

  final String title;

  final String content;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: vpTextStyle.captionRegular.copyColor(vpColor.textTertiary),
        ),
        Text(
          content,
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }
}
