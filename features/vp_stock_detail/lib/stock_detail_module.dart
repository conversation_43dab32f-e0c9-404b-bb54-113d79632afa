import 'package:flutter/src/widgets/localizations.dart';
import 'package:stock_detail/core/repository/stock_detail_repository.dart';
import 'package:stock_detail/core/service/stock_detail_service.dart';
import 'package:stock_detail/fu_stock_detail/fu_stock_detail_page.dart';
import 'package:stock_detail/model/enum/trading_view_period.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/stock_detail_page.dart';
import 'package:stock_detail/tabs/stock_trading_tab/trading_view/trading_fullscreen_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/stock_detail/stock_detail_args.dart';

import 'router/stock_detail_router.dart';

class VPStockDetailModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton(() => StockDetailService(service()));
    service.registerLazySingleton<StockDetailRepository>(
      () => StockDetailRepositoryImpl(stockDetailService: service()),
    );

    service.registerLazySingleton<StockDetailNavigator>(
      () => StockDetailNavigatorImpl(),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VPStockDetailRouter.stockDetail.routeName,
        name: VPStockDetailRouter.stockDetail.routeName,
        builder: (_, state) {
          return StockDetailPage(
            args: StockDetailArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          );
        },
      ),
      GoRoute(
        path: VPStockDetailRouter.fuStockDetail.routeName,
        name: VPStockDetailRouter.fuStockDetail.routeName,
        builder: (_, state) {
          final data = state.extra as Map?;

          final symbol = data?['symbol'];

          return FuStockDetailPage(symbol: symbol);
        },
      ),
      GoRoute(
        path: VPStockDetailRouter.tradingViewFullScreen.routeName,
        name: VPStockDetailRouter.tradingViewFullScreen.routeName,
        builder: (context, state) {
          final query = state.extra as Map<String, dynamic>;

          final symbol = query['symbol'] as String;
          final period = TradingViewPeriod.values.byName(query['period']!);

          return TradingViewFullScreen(symbol: symbol, period: period);
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [];
  }

  @override
  String modulePath() {
    return 'vpStockDetail';
  }
}
