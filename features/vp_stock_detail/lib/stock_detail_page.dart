import 'package:flutter/material.dart';
import 'package:stock_detail/stock_detail_content_view.dart';
import 'package:stock_detail/stock_detail_navigator.dart';
import 'package:stock_detail/widgets/stock_detail_shimmer_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/stock_detail/stock_detail_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_design_system/gen/assets.gen.dart' as assets;

import 'stock_detail_cubit.dart';

class StockDetailPage extends StatelessWidget {
  const StockDetailPage({required this.args, super.key});

  final StockDetailArgs args;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StockDetailCubit>(
      create: (_) => StockDetailCubit(symbol: args.symbol)..loadData(),
      child: <PERSON><PERSON><PERSON>er<StockDetailCubit, StockDetailState>(
        builder: (context, state) {
          final stock = state.stockInfo;

          final marketCode = stock?.marketCode;

          final marketCodeName =
              marketCode != null ? '(${marketCode.name})' : '';

          return VPScaffold(
            appBar: VPAppBar.layer(
              title: '${state.symbol} $marketCodeName',
              actions: [
                if (isLoggedIn)
                  IconButton(
                    onPressed: () => addSymbolToWatchlist(context),
                    icon: DesignAssets.icons.add.icAddSquare.svg(
                      color: vpColor.iconPrimary,
                    ),
                  ),
                IconButton(
                  onPressed: () => openSearchPage(context),
                  icon: assets.Assets.icons.icSearch.svg(
                    color: vpColor.iconPrimary,
                  ),
                ),
              ],
            ),
            body:
                state.apiStatus.isError
                    ? ErrorWithRetryView(onRetry: () => onRetry(context))
                    : buildContentView(stock),
          );
        },
      ),
    );
  }

  Widget buildContentView(StockInfoModel? stock) {
    return stock == null
        ? const StockDetailShimmerView()
        : StockDetailContentView(stock: stock, initialTab: args.initialTab);
  }

  void onRetry(BuildContext context) {
    context.read<StockDetailCubit>().loadData();
  }

  void openSearchPage(BuildContext context) async {
    final data = await stockDetailNavigator.openSearchPage(
      context,
      args: SearchArgs(hint: 'Nhập mã tìm kiếm'),
    );

    if (data is! StockModel || !context.mounted) return;

    context.read<StockDetailCubit>().onSymbolChanged(data.symbol);
  }

  void addSymbolToWatchlist(BuildContext context) {
    stockDetailNavigator.openAddSymbolToWatchlistSelectorBottomSheet(
      context,
      symbols: [args.symbol],
    );
  }
}
