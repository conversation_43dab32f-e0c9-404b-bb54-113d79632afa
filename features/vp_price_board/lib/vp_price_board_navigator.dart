import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/router/vp_price_board_router.dart';
import 'package:vp_price_board/vp_price_board.dart';
import 'package:vp_stock_common/model/stock_detail/stock_detail_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

PriceBoardNavigator get priceBoardNavigator =>
    GetIt.instance.get<PriceBoardNavigator>();

abstract class PriceBoardNavigator {
  Future openPriceBoardPage(BuildContext context);

  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
  });

  Future openFuStockDetailPage(BuildContext context, {required String symbol});

  Future openSearchPage(
    BuildContext context, {
    SearchArgs? args,
    SearchExtraArgs? extraArgs,
  });

  Future openWatchlistSelectorBottomSheet(
    BuildContext context, {
    bool showAddWatchlistButton = false,
    bool showSuggestionWatchlist = true,
    bool showHoldingWatchlist = true,
    VoidCallback? onAuthCallback,
  });

  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  });

  Future openAddSymbolsToWatchlistBottomSheet(
    BuildContext context, {
    required WatchlistModel watchlist,
    List<StockType>? stockTypes,
  });

  Future openWatchlistDetailBottomSheet(
    BuildContext context, {
    required WatchlistModel watchlist,
  });

  void openMainStockPage(BuildContext context, {StockHomeArgs? args});

  void openSignInRequiredDialog(BuildContext context);
}

class PriceBoardNavigatorImpl extends PriceBoardNavigator {
  @override
  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
  }) {
    return context.pushNamed(
      VpPriceBoardRouter.stockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }

  @override
  Future openFuStockDetailPage(BuildContext context, {required String symbol}) {
    return context.push('/fuStockDetail', extra: {'symbol': symbol});
  }

  @override
  Future openSearchPage(
    BuildContext context, {
    SearchArgs? args,
    SearchExtraArgs? extraArgs,
  }) {
    return context.pushNamed(
      VPStockCommonRouter.search.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
      extra: extraArgs,
    );
  }

  @override
  Future openWatchlistSelectorBottomSheet(
    BuildContext context, {
    bool showAddWatchlistButton = false,
    bool showSuggestionWatchlist = true,
    bool showHoldingWatchlist = true,
    VoidCallback? onAuthCallback,
  }) {
    return VPPopup.bottomSheet(
          WatchlistSelectorBottomSheet(
            showAddWatchlistButton: showAddWatchlistButton,
            showSuggestionWatchlist: showSuggestionWatchlist,
            showHoldingWatchlist: showHoldingWatchlist,
            onAuthCallback: onAuthCallback,
          ),
        )
        .copyWith(
          button: const VpBottomSheetCancelButtonView(),
          contentPadding: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(8),
          icon: const SizedBox.shrink(),
        )
        .showAction(context);
  }

  @override
  Future openAddSymbolToWatchlistSelectorBottomSheet(
    BuildContext context, {
    required List<String> symbols,
  }) {
    return VPPopup.bottomSheet(
      AddSymbolToWatchListBottomSheet(symbols: symbols),
    ).showSheet(context);
  }

  @override
  Future openAddSymbolsToWatchlistBottomSheet(
    BuildContext context, {
    required WatchlistModel watchlist,
    List<StockType>? stockTypes,
  }) {
    return VPPopup.bottomSheet(
      AddSymbolsToWatchListBottomSheet(
        watchlist: watchlist,
        stockTypes: stockTypes,
      ),
    ).showSheet(context);
  }

  @override
  Future openWatchlistDetailBottomSheet(
    BuildContext context, {
    required WatchlistModel watchlist,
  }) {
    return VPPopup.bottomSheet(
      WatchlistDetailBottomSheet(watchlist: watchlist),
    ).showSheet(context);
  }

  @override
  void openMainStockPage(BuildContext context, {StockHomeArgs? args}) {
    context.pushNamed(
      VpPriceBoardRouter.mainStockHome.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
    );
  }

  @override
  void openSignInRequiredDialog(BuildContext context) {
    VPPopup.custom(
      child: SignInRequiredDialog(
        onSignIn:
            () => context.push(
              VpPriceBoardRouter.signIn.routeName,
              extra: SignInArgs(
                onSignInSuccess: () => openPriceBoardPage(context),
              ),
            ),
      ),
    ).showDialog(context);
  }

  @override
  Future openPriceBoardPage(BuildContext context) {
    return context.push(VpPriceBoardRouter.priceBoard.routeName);
  }
}
