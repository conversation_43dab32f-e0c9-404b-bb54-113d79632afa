import 'package:flutter/material.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_price_board/widget/stock_board/stock_board_item_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class StockListView extends StatelessWidget {
  const StockListView({
    required this.items,
    required this.onRefresh,
    required this.columns,
    this.controller,
    super.key,
  });

  final List<StockInfoModel> items;

  final ScrollController? controller;

  final RefreshCallback onRefresh;

  final List<SortColumn> columns;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        itemExtent: 40,
        physics: const AlwaysScrollableScrollPhysics(),
        controller: controller,
        padding: EdgeInsets.zero,
        itemBuilder: (_, index) {
          return StockBoardItemView(
            item: items[index],
            columns: columns,
            onTap:
                (item) =>
                    () => priceBoardNavigator.openStockDetailPage(
                      context,
                      args: StockDetailArgs(symbol: item.symbol),
                    ),
          );
        },
        itemCount: items.length,
      ),
    );
  }
}
