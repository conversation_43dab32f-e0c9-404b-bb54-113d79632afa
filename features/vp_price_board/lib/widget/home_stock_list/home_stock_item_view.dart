import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_price_board/widget/home_stock_list/chart_view/home_stock_chart_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class HomeStockItemView extends StatelessWidget {
  const HomeStockItemView({
    required this.stock,
    this.chartData,
    this.itemHeight = 64,
    super.key,
  });

  final StockInfoModel stock;

  final List<ChartData>? chartData;

  final double itemHeight;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          () => priceBoardNavigator.openStockDetailPage(
            context,
            args: StockDetailArgs(symbol: stock.symbol),
          ),
      child: SizedBox(
        height: itemHeight,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stock.symbol,
                    style: vpTextStyle.subtitle14.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                  VPTotalTradingVolumeItemView(
                    symbol: stock.symbol,
                    initTotalVolume: stock.totalTradingVolume,
                    builder: (volume) {
                      final vol = FormatUtils.formatVol(volume);

                      return Text(
                        vol == '-' ? '-' : '$vol CP',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: vpTextStyle.captionSemiBold.copyColor(
                          vpColor.textSecondary,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            Expanded(
              flex: 2,
              child: IgnorePointer(
                child: SizedBox(
                  height: 40,
                  child: HomeStockChartView(
                    chartData: chartData ?? [],
                    stock: stock,
                  ),
                ),
              ),
            ),

            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  VPClosePriceItemView.stock(
                    stock: stock,
                    styleBuilder: (closePrice, color) {
                      return vpTextStyle.subtitle14.copyColor(
                        color ?? vpColor.textPrimary,
                      );
                    },
                  ),

                  VPProfitItemView(
                    stock: stock,
                    style: vpTextStyle.captionMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
