import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class StockBoardCachedItemView extends StatelessWidget {
  const StockBoardCachedItemView({
    required this.symbol,
    required this.columns,
    super.key,
  });

  final String symbol;

  final List<SortColumn> columns;

  Widget mapItemView(SortColumn column) {
    switch (column.sortType) {
      case StockInfoFieldType.symbol:
        return Expanded(
          flex:
              columns
                  .firstWhere((e) => e.sortType == StockInfoFieldType.symbol)
                  .flex,
          child: buildSymbolView(),
        );
      case StockInfoFieldType.closePrice:
        return Expanded(
          flex:
              columns
                  .firstWhere(
                    (e) => e.sortType == StockInfoFieldType.closePrice,
                  )
                  .flex,
          child: buildClosePriceView(),
        );

      case StockInfoFieldType.change:
        return Expanded(
          flex:
              columns
                  .firstWhere((e) => e.sortType == StockInfoFieldType.change)
                  .flex,
          child: buildValueChangeView(),
        );
      case StockInfoFieldType.percentChange:
        return Expanded(
          flex:
              columns
                  .firstWhere(
                    (e) => e.sortType == StockInfoFieldType.percentChange,
                  )
                  .flex,
          child: buildPercentChangeView(),
        );

      case StockInfoFieldType.vol:
        return Expanded(
          flex:
              columns
                  .firstWhere((e) => e.sortType == StockInfoFieldType.vol)
                  .flex,
          child: buildVolumeView(),
        );
      case StockInfoFieldType.diff:
        return Expanded(
          flex:
              columns
                  .firstWhere((e) => e.sortType == StockInfoFieldType.diff)
                  .flex,
          child: buildDiffView(),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          () => priceBoardNavigator.openStockDetailPage(
            context,
            args: StockDetailArgs(symbol: symbol),
          ),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: vpColor.strokeNormal)),
        ),
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
        child: Row(children: columns.map((e) => mapItemView(e)).toList()),
      ),
    );
  }

  Widget buildSymbolView() {
    return AutoSizeText(
      symbol,
      maxLines: 1,
      minFontSize: 10,
      softWrap: true,
      textAlign: TextAlign.start,
      style: vpTextStyle.subtitle14.copyColor(vpColor.textTertiary),
    );
  }

  Widget buildDiffView() {
    return Text(
      '-',
      style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
      textAlign: TextAlign.end,
    );
  }

  Widget buildClosePriceView() {
    return Text(
      '-',
      style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
      textAlign: TextAlign.end,
    );
  }

  Widget buildValueChangeView() {
    return Text(
      '-',
      style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
      textAlign: TextAlign.end,
    );
  }

  Widget buildPercentChangeView() {
    return Text(
      '-',
      style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
      textAlign: TextAlign.end,
    );
  }

  Widget buildVolumeView() {
    return Text(
      '-',
      style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
      textAlign: TextAlign.end,
    );
  }
}
