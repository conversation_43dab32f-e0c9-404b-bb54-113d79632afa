import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/model/enum/priceboard_tab.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

import 'tabs/price_board_tab_view.dart';

class PriceBoardPage extends StatefulWidget {
  const PriceBoardPage({super.key});

  @override
  State<PriceBoardPage> createState() => _PriceBoardPageState();
}

class _PriceBoardPageState extends State<PriceBoardPage> {
  PriceBoardTabViewEnum? tab;

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      appBar: VPAppBar.layer(
        title: 'Bảng giá',
        leading: BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) {
            return state.status != AuthStatus.logined
                ? IconButton(
                  onPressed: () async {
                    context.push('/signIn');
                  },
                  icon: Icon(Icons.person, color: themeData.gray500),
                )
                : IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Assets.icons.appbar.icBack.svg(
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                );
          },
        ),
        actions: [
          IconButton(
            onPressed: () => openSearchPage(context),
            icon: Assets.icons.icSearch.svg(
              colorFilter: ColorFilter.mode(
                vpColor.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          const SizedBox(height: 12),

          const MarketIndexList(),

          const SizedBox(height: 12),

          Expanded(child: PriceBoardTabView(onTabChange: onTabChange)),
        ],
      ),
    );
  }

  void onTabChange(PriceBoardTabViewEnum? tab) {
    this.tab = tab;
  }

  Future openSearchPage(BuildContext context) {
    if (tab == PriceBoardTabViewEnum.bond) {
      return stockCommonNavigator.openBondSearchPage(context);
    }

    return priceBoardNavigator.openSearchPage(
      context,
      args: SearchArgs(
        marketCodes: [
          MarketCode.HOSE,
          MarketCode.HNX,
          MarketCode.UPCOM,
          MarketCode.FU,
        ],
        itemAction: SearchItemAction.openDetail,
      ),
      extraArgs: SearchExtraArgs(
        onAddSymbol: (stock) => addSymbolToWatchList(context, stock),
      ),
    );
  }

  void addSymbolToWatchList(BuildContext context, StockModel stock) {
    priceBoardNavigator.openAddSymbolToWatchlistSelectorBottomSheet(
      context,
      symbols: [stock.symbol],
    );
  }
}
