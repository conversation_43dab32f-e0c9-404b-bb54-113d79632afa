import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_portfolio/generated/l10n.dart';
import 'package:vp_portfolio/model/copiers_info_of_account_model.dart';
import 'package:vp_portfolio/model/copytrade_circular_chart_data.dart';
import 'package:vp_portfolio/screen/holding_portfolio/detail/cubit/detail_holding_cubit.dart';
import 'package:vp_portfolio/screen/holding_portfolio/managing/bloc/listen_refresh_data.dart';
import 'package:vp_portfolio/utils/color_exts.dart';
import 'package:vp_portfolio/widgets/app_lifecycle_listener.dart';
import 'package:vp_portfolio/widgets/copytrade_circular_chart.dart';
import 'package:vp_portfolio/widgets/legend/legend_horizontal.dart';
import 'package:vp_portfolio/widgets/stock_bottom_sheet_widget.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide CommonColorUtils;

class StockTabPage extends StatefulWidget {
  final DataMp param;

  const StockTabPage({Key? key, required this.param}) : super(key: key);

  @override
  State<StockTabPage> createState() => _StockTabPageState();
}

class _StockTabPageState extends AppLifeCycleListener<StockTabPage>
    with AutomaticKeepAliveClientMixin<StockTabPage> {
  StreamSubscription? subscription;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    subscription = ListenRefreshData().controller.stream.listen((data) {
      if (data) {
        context.read<DetailHoldingCubit>().fetchData(
          widget.param.copierId ?? 0,
        );
        context.read<DetailHoldingCubit>().getWithDrawableAmount(
          widget.param.copierId ?? -1,
        );
      }
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }

  @override
  void onResumeApp() {
    if (mounted) {}
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocBuilder<DetailHoldingCubit, DetailHoldingState>(
      builder: (context, state) {
        List<CopyTradeCircularChartData> setChartData() {
          List<Color> colors = [
            themeData.yellow,
            themeData.primary,
            themeData.blueChart,
            themeData.ceilingColor,
            themeData.gray500,
          ];
          final List<CopyTradeCircularChartData> _charts = [];
          final list =
              (state.data?.stockList ?? [])
                ..sort((a, b) => a.rate < b.rate ? 1 : -1);
          if (list.isNotEmpty) {
            final int length = list.length <= 4 ? list.length : 4;
            for (int i = 0; i < length; i++) {
              _charts.add(
                CopyTradeCircularChartData(
                  x: list[i].symbol ?? '',
                  y: list[i].rate,
                  color: colors[i],
                ),
              );
            }
            if (list.length > 4) {
              double y =
                  100 -
                  (_charts.map((e) => e.y).fold(0, (a, b) => a + b)) -
                  (state.data?.cashRatio ?? 0);
              _charts.add(
                CopyTradeCircularChartData(
                  x: VPPortfolioLocalize.current.portfolio_other,
                  y: double.parse(y.toStringAsFixed(2)),
                  color: colors[4],
                ),
              );
            }
          }
          return _charts;
        }

        final List<CopyTradeCircularChartData> _chartData = setChartData();
        _chartData.insert(
          0,
          CopyTradeCircularChartData(
            x: 'Tiền',
            y: state.data?.cashRatio ?? 0,
            color: themeData.red,
          ),
        );
        return Stack(
          children: [
            ListView(
              padding: const EdgeInsets.only(bottom: 48),
              children: [
                if (_chartData.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(width: 8),
                        CopyTradeCircularChart(
                          chartData: _chartData,
                          enableTooltip: false,
                        ),
                        const SizedBox(width: 24),
                        Expanded(child: LegendVertical(chartData: _chartData)),
                      ],
                    ),
                  ),
                const SizedBox(height: 24),

                if (state.data?.stockList != null) ...[
                  StockTable(
                    copierId: widget.param.copierId ?? -1,
                    stockList:
                        state.data!.stockList!
                            .map(
                              (e) => DataStockItem(
                                symbol: e.symbol,
                                price: e.closePrice,
                                volume: e.quantity,
                                value: e.value,
                                percent: e.rate,
                                awardCash: e.awardCash,
                                awardStock: e.awardStock,
                                color: CommonColorUtils.colorByPrice(
                                  referencePrice:
                                      (e.detail?.reference ?? 0).toDouble(),
                                  currentPrice: e.closePrice ?? 0,
                                  ceilingPrice:
                                      (e.detail?.ceiling ?? 0).toDouble(),
                                  floorPrice: (e.detail?.floor ?? 0).toDouble(),
                                ),
                                priorityBuyPending: e.priorityBuyPending,
                                rightsEndTime: e.rightsEndTime,
                                rightQuantity: e.rightQuantity,
                                transactionVolume:
                                    e.quantity -
                                    (e.awardStock ?? 0) -
                                    (e.priorityBuyPending ?? 0),
                              ),
                            )
                            .toList(),
                  ),
                ],
              ],
            ),
            BlocBuilder<DetailHoldingCubit, DetailHoldingState>(
              buildWhen: (previous, current) => previous.data != current.data,
              builder: (context, state) {
                final status = state.data?.status;

                final showInvestButton = status == 1 || status == 4;
                if (!showInvestButton) return const SizedBox.shrink();
                return Positioned(
                  bottom: 0,
                  right: 0,
                  left: 0,
                  child: VpsButton.primarySmall(
                    title: VPPortfolioLocalize.current.portfolio_moveInvest,
                    onPressed:
                        () => context.read<DetailHoldingCubit>().onMoreInvest(
                          context,
                        ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}

class StockTable extends StatelessWidget {
  const StockTable({Key? key, required this.stockList, required this.copierId})
    : super(key: key);

  final List<DataStockItem> stockList;
  final int copierId;

  @override
  Widget build(BuildContext context) {
    final defaultTableHeaderTextStyle = vpTextStyle.captionRegular?.copyWith(
      color: vpColor.textPrimary,
    );
    final defaultTableBodyTextStyle = vpTextStyle.captionRegular?.copyWith(
      color: vpColor.textPrimary,
    );
    List<int> expands = [1, 1, 2, 1];
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: expands[0],
              child: Text('Mã/\nTổng KL', style: defaultTableHeaderTextStyle),
            ),
            Expanded(
              flex: expands[1],
              child: Text(
                'Giá TT',
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
            Expanded(
              flex: expands[2],
              child: Text(
                'Giá trị TT',
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
            Expanded(
              flex: expands[3],
              child: Text(
                'Tỷ trọng',
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
          ],
        ),
        Column(
          children:
              stockList
                  .map(
                    (e) => Column(
                      children: [
                        InkWell(
                          onTap: () {
                            context.pushNamed(
                              '/stockDetail',
                              queryParameters:
                                  StockDetailArgs(
                                    symbol: e.symbol!,
                                  ).toQueryParams(),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: expands[0],
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${e.symbol}',
                                        style: vpTextStyle.subtitle16?.copyWith(
                                          color: vpColor.textPrimary,
                                        ),
                                      ),
                                      Text(
                                        '${e.volume?.toFormat3()}',
                                        style: defaultTableBodyTextStyle,
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: expands[1],
                                  child: Text(
                                    e.price != null
                                        ? e.price!.getPriceFormatted(
                                          convertToThousand: true,
                                        )
                                        : '',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                                Expanded(
                                  flex: expands[2],
                                  child: Text(
                                    '${e.value?.toMoney()}',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                                Expanded(
                                  flex: expands[3],
                                  child: Text(
                                    '${e.percent?.getMarketChangePercentDisplay()}',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        _AwardWidget(
                          title: 'Giao dịch',
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle,
                          value: e.transactionVolume?.toMoney(symbol: 'CP'),
                          show:
                              (e.awardStock ?? 0) != 0 ||
                              (e.priorityBuyPending ?? 0) != 0,
                        ),
                        _AwardWidget(
                          title: 'Quyền CP chờ về',
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle,
                          value: e.awardStock?.toMoney(symbol: 'CP'),
                          show: (e.awardStock ?? 0) != 0,
                        ),
                        _AwardWidget(
                          title: 'Quyền mua',
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle,
                          value: e.priorityBuyPending?.toMoney(symbol: 'CP'),
                          show: (e.priorityBuyPending ?? 0) != 0,
                        ),
                        _StockRightRegisterWidget(
                          rightsEndTime: e.rightsEndTime ?? '',
                          show: _isShowStockRightRegister(e),
                          onTap: () {
                            context
                                .read<DetailHoldingCubit>()
                                .getDetailStockRight(
                                  symbol: e.symbol ?? '',
                                  copierId: copierId,
                                );
                          },
                        ),
                        const SizedBox(height: 4),
                        Divider(thickness: 1, color: vpColor.strokeNormal),
                      ],
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }

  bool _isShowStockRightRegister(DataStockItem data) {
    if (data.rightsEndTime.isNullOrEmpty) return false;
    String formattedDate = (data.rightsEndTime ?? '')
        .split('/')
        .reversed
        .join('-');
    return DateTime.parse(formattedDate).isAfter(DateTime.now()) &&
        (data.rightQuantity ?? 0) > 0;
  }
}

class _AwardWidget extends StatelessWidget {
  final String title;
  final String? value;
  final TextStyle? defaultTableBodyTextStyle;
  final bool show;

  const _AwardWidget({
    Key? key,
    required this.title,
    required this.show,
    this.value,
    this.defaultTableBodyTextStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!show) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Text(
            title,
            style: defaultTableBodyTextStyle?.copyWith(
              color: vpColor.textTertiary,
            ),
          ),
          Expanded(
            child: Text(
              value!,
              textAlign: TextAlign.right,
              style: vpTextStyle.captionSemiBold?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _StockRightRegisterWidget extends StatelessWidget {
  final String rightsEndTime;
  final bool show;
  final VoidCallback onTap;

  const _StockRightRegisterWidget({
    Key? key,
    required this.rightsEndTime,
    required this.show,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!show) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0, top: 8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: vpColor.backgroundAccentBlue.withOpacity(0.16),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              Assets.icons.info.icInfo.path,
              package: 'vp_common',
              width: 24,
              colorFilter: ColorFilter.mode(
                vpColor.iconAccentBlue,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Hạn đăng ký quyền mua: $rightsEndTime',
              textAlign: TextAlign.right,
              style: vpTextStyle.captionMedium?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 2.0),
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: onTap,
                child: Text(
                  'Đăng ký',
                  style: vpTextStyle.subtitle14?.copyWith(
                    color: vpColor.textAccentBlue,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
