import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_portfolio/model/copytrade_circular_chart_data.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class LegendVertical extends StatelessWidget {
  const LegendVertical({Key? key, required this.chartData}) : super(key: key);
  final List<CopyTradeCircularChartData> chartData;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 4.0,
      runSpacing: 16.0,
      children:
          chartData
              .map(
                (e) => SizedBox(
                  width: 90.0,
                  child: InkWell(
                    onTap: () {
                      if (e.x.length == 3) {
                        context.pushNamed(
                          '/stockDetail',
                          queryParameters:
                              StockDetailArgs(
                                symbol: e.x!,
                              ).toQueryParams(),
                        );
                      }
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 20,
                          height: 4,
                          decoration: BoxDecoration(
                            color: e.color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              e.x,
                              style: vpTextStyle.captionMedium?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${e.y.toStringAsFixed(2)}%',
                              style: vpTextStyle.captionSemiBold?.copyWith(
                                color: vpColor.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              )
              .toList(),
    );
  }
}
