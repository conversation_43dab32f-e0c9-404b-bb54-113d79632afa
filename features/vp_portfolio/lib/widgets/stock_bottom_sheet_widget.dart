import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_portfolio/generated/l10n.dart';
import 'package:vp_portfolio/model/copytrade_circular_chart_data.dart';
import 'package:vp_portfolio/model/detail_category_holding_model.dart';
import 'package:vp_portfolio/utils/color_exts.dart';
import 'package:vp_portfolio/widgets/copytrade_circular_chart.dart';
import 'package:vp_portfolio/widgets/legend/legend_horizontal.dart';
import 'package:vp_stock_common/vp_stock_common.dart' hide CommonColorUtils;

Future showStockBottomSheetWidget(
  BuildContext context, {
  DetailCategoryHoldingModel? data,
}) {
  List<CopyTradeCircularChartData> setChartData() {
    final List<CopyTradeCircularChartData> _charts = [];
    final list =
        (data?.stockList ?? [])..sort((a, b) => a.rate < b.rate ? 1 : -1);
    if (list.isNotEmpty) {
      final int length = list.length <= 4 ? list.length : 4;
      for (int i = 0; i < length; i++) {
        _charts.add(
          CopyTradeCircularChartData(
            x: list[i].symbol ?? '',
            y: list[i].rate,
            color: CommonColorUtils.colorsChart[i],
          ),
        );
      }
      if (list.length > 4) {
        double y = 100 - (_charts.map((e) => e.y).fold(0, (a, b) => a + b));
        _charts.add(
          CopyTradeCircularChartData(
            x: VPPortfolioLocalize.current.portfolio_other,
            y: double.parse(y.toStringAsFixed(2)),
            color: CommonColorUtils.colorsChart[4],
          ),
        );
      }
    }
    return _charts;
  }

  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      final List<CopyTradeCircularChartData> _chartData = setChartData();

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        height:
            MediaQuery.of(context).size.height -
            MediaQueryData.fromWindow(window).padding.top -
            kToolbarHeight,
        alignment: Alignment.bottomCenter,
        clipBehavior: Clip.hardEdge,
        decoration: ShapeDecoration(
          color: themeData.bgPopup,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
        ),
        child: ListView(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 56,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: themeData.gray300,
                ),
              ),
            ),
            Text(
              VPPortfolioLocalize.current.portfolio_stock,
              style: vpTextStyle.subtitle16.copyColor(themeData.black),
            ),
            Visibility(
              visible: _chartData.isNotEmpty && _chartData[0].y > 0,
              child: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: CopyTradeCircularChart(
                          chartData: _chartData,
                          enableTooltip: false,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 24),
                        child: LegendVertical(chartData: _chartData),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            if (data?.stockList != null) ...[
              StockTable(
                stockList:
                    data!.stockList!
                        .map(
                          (e) => DataStockItem(
                            symbol: e.symbol,
                            price: e.closePrice,
                            volume: e.quantity,
                            value: e.value,
                            percent: e.rate,
                            awardCash: e.awardCash,
                            awardStock: e.awardStock,
                            color: CommonColorUtils.colorByPrice(
                              referencePrice:
                                  (e.detail?.reference ?? 0).toDouble(),
                              currentPrice: e.closePrice ?? 0,
                              ceilingPrice: (e.detail?.ceiling ?? 0).toDouble(),
                              floorPrice: (e.detail?.floor ?? 0).toDouble(),
                            ),
                            priorityBuyPending: e.priorityBuyPending,
                          ),
                        )
                        .toList(),
              ),
            ],
          ],
        ),
      );
    },
  );
}

class DataStockItem {
  final String? symbol;
  final int? price;
  final int? volume;
  final int? value;
  final double? percent;
  final Color? color;
  num? awardCash;
  num? awardStock;
  num? priorityBuyPending;
  num? transactionVolume;
  num? rightQuantity;
  String? rightsEndTime;

  DataStockItem({
    this.symbol,
    this.price,
    this.volume,
    this.value,
    this.percent,
    this.color,
    this.awardStock,
    this.awardCash,
    this.priorityBuyPending,
    this.transactionVolume,
    this.rightQuantity,
    this.rightsEndTime,
  });
}

class StockTable extends StatelessWidget {
  const StockTable({Key? key, required this.stockList}) : super(key: key);

  final List<DataStockItem> stockList;

  @override
  Widget build(BuildContext context) {
    final defaultTableHeaderTextStyle = vpTextStyle.captionMedium.copyColor(
      vpColor.textPrimary,
    );
    final defaultTableBodyTextStyle = vpTextStyle.subtitle14.copyColor(
      vpColor.textPrimary,
    );
    List<int> expands = [1, 1, 2, 1];
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: expands[0],
              child: Text(
                VPPortfolioLocalize.current.portfolio_item,
                style: defaultTableHeaderTextStyle,
              ),
            ),
            Expanded(
              flex: expands[1],
              child: Text(
                VPPortfolioLocalize.current.portfolio_volume,
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
            Expanded(
              flex: expands[2],
              child: Text(
                VPPortfolioLocalize.current.portfolio_value,
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
            Expanded(
              flex: expands[3],
              child: Text(
                '%',
                textAlign: TextAlign.right,
                style: defaultTableHeaderTextStyle,
              ),
            ),
          ],
        ),
        Column(
          children:
              stockList
                  .map(
                    (e) => Column(
                      children: [
                        InkWell(
                          onTap: () {
                            context.pushNamed(
                              '/stockDetail',
                              queryParameters:
                                  StockDetailArgs(
                                    symbol: e.symbol!,
                                  ).toQueryParams(),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: expands[0],
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${e.symbol}',
                                        style: vpTextStyle.subtitle16.copyColor(
                                          vpColor.textPrimary,
                                        ),
                                      ),
                                      Text(
                                        e.price != null
                                            ? e.price!.getPriceFormatted(
                                              convertToThousand: true,
                                            )
                                            : '',
                                        style: vpTextStyle.subtitle16.copyColor(
                                          e.color ?? vpColor.textPrimary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: expands[1],
                                  child: Text(
                                    '${e.volume?.toFormat()}',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                                Expanded(
                                  flex: expands[2],
                                  child: Text(
                                    '${e.value?.toMoney()}',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                                Expanded(
                                  flex: expands[3],
                                  child: Text(
                                    '${e.percent?.getMarketChangePercentDisplay()}',
                                    textAlign: TextAlign.right,
                                    style: defaultTableBodyTextStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        _AwardWidget(
                          title:
                              VPPortfolioLocalize.current.portfolio_awardCash,
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle!,
                          value: e.awardCash?.toMoney(),
                          show: (e.awardCash ?? 0) != 0,
                        ),
                        _AwardWidget(
                          title:
                              VPPortfolioLocalize.current.portfolio_awardStock,
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle,
                          value: e.awardStock?.toMoney(symbol: 'CP'),
                          show: (e.awardStock ?? 0) != 0,
                        ),
                        _AwardWidget(
                          title:
                              VPPortfolioLocalize
                                  .current
                                  .portfolio_registeredPurchaseRight,
                          defaultTableBodyTextStyle: defaultTableBodyTextStyle!,
                          value: e.priorityBuyPending?.toMoney(symbol: 'CP'),
                          show: (e.priorityBuyPending ?? 0) != 0,
                        ),
                        const SizedBox(height: 4),
                        Divider(height: 1, color: themeData.gray100),
                      ],
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }
}

class _AwardWidget extends StatelessWidget {
  final String title;
  final String? value;
  final TextStyle defaultTableBodyTextStyle;
  final bool show;

  const _AwardWidget({
    Key? key,
    required this.title,
    required this.show,
    this.value,
    required this.defaultTableBodyTextStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!show) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Text(
            title,
            style: defaultTableBodyTextStyle.copyWith(color: themeData.gray700),
          ),
          Expanded(
            child: Text(
              value!,
              textAlign: TextAlign.right,
              style: defaultTableBodyTextStyle,
            ),
          ),
        ],
      ),
    );
  }
}
