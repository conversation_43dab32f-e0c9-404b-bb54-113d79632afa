name: vp_portfolio
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_stock_common:
    path: ../../library/vp_stock_common
  vp_core:
    path: ../../library/vp_core
  equatable:
  syncfusion_flutter_pdfviewer:
  just_the_tooltip:
  flutter_keyboard_visibility:
  url_launcher:
  shimmer:
dev_dependencies:
  flutter_lints: ^5.0.0
  test: ^1.24.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  

flutter_intl:
  enabled: true
  class_name: VPPortfolioLocalize
  
flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    
    outputs:
      class_name: VpPortfolioAssets
      package_parameter_enabled: true
    

  # Optional
  integrations:
    image: true
    flutter_svg: true
flutter:
  assets:
    - assets/icons/
    - assets/images/