name: vp_trading
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_keyboard_visibility:
  auto_size_text_field:
  marquee:
  textfield_have_range_button:
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common
  data_table_2: ^2.6.0

dependency_overrides:
   collection: 1.19.1
   intl: ^0.19.0
dev_dependencies:
  lints: ^3.0.0
  test: ^1.24.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  flutter_lints:

flutter_intl:
  enabled: true
  class_name: VPTradingLocalize
  
flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    
    outputs:
      class_name: VpTradingAssets
      package_parameter_enabled: true
    

  # Optional
  integrations:
    image: true
    flutter_svg: true
flutter:
  assets:
    - assets/icons/
    - assets/images/