import 'package:flutter/services.dart';
import 'package:vp_trading/core/extension/ext.dart';

TextInputFormatter get removeZeroStartInputFormatter =>
    TextInputFormatter.withFunction((_, newValue) {
      if (newValue.text.startsWith(RegExp(r'^-?0\d+'))) {
        final newText = newValue.text.replaceFirst('0', '');
        return newValue.copyWith(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }
      return newValue;
    });

List<TextInputFormatter> get priceInputFormatter => [
  FilteringTextInputFormatter.deny(RegExp(r'^([.,])')),
  FilteringTextInputFormatter.allow(RegExp(r'^[\d,]+([.,])?\d{0,2}')),
  FilteringTextInputFormatter.deny(RegExp(r',$'), replacementString: '.'),
  FilteringTextInputFormatter.deny(RegExp(r',(?=\d+)'), replacementString: ''),
  // Giới hạn tối đa 11 ký tự (bao gồm dấu .)
  TextInputFormatter.withFunction((oldValue, newValue) {
    if (newValue.text.length > 11) {
      return oldValue;
    }
    return newValue;
  }),
];

List<TextInputFormatter> get volumeInputFormatter => [
  FilteringTextInputFormatter.deny(RegExp(r'\D$')),
  TextInputFormatter.withFunction((oldValue, newValue) {
    if (newValue.text.isEmpty) return newValue;
    final newText = newValue.text.volume.volumeString;
    if (newText.length > 12) return oldValue;
    return newValue.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }),
];
