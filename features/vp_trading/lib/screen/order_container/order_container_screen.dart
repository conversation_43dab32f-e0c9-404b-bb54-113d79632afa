import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/order_container/order_container/order_container_cubit.dart';
import 'package:vp_trading/screen/order_container/enum/enum_tabbar_order.dart';
import 'package:vp_trading/screen/order_container/normal_order/normal_order_screen.dart';
import 'package:vp_trading/screen/order_container/transaction_history/transaction_history_screen.dart';
import 'package:vp_trading/vp_trading.dart';

class OrderContainerScreen extends StatefulWidget {
  const OrderContainerScreen({super.key});

  @override
  State<OrderContainerScreen> createState() => _OrderContainerScreenState();
}

class _OrderContainerScreenState extends State<OrderContainerScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  late final OrderContainerCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = OrderContainerCubit();
    _tabController = TabController(
      length: EnumTabbarOrder.values.length,
      vsync: this,
      initialIndex: EnumTabbarOrder.normal.index,
    );
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      final newTab = EnumTabbarOrder.values[_tabController.index];
      _cubit.changeTab(newTab);
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OrderContainerCubit>.value(
      value: _cubit..init(),
      child: VPScaffold(
        body: SafeArea(
          child: Column(
            children: [_buildAppBar(), Expanded(child: _buildTabBarView())],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return BlocBuilder<OrderContainerCubit, OrderContainerState>(
      bloc: _cubit,
      builder: (context, state) {
        return VPAppBar.layer(
          backgroundColor: vpColor.backgroundElevation0,
          title: VPTradingLocalize.current.trading_command_history,
          actions: [_buildSearchButton(), const SizedBox(width: 16)],
        );
      },
    );
  }

  Widget _buildSearchButton() {
    return GestureDetector(
      onTap: _showSearchOrderContainer,
      child: DesignAssets.icons.icSearch.svg(
        colorFilter: ColorFilter.mode(vpColor.iconPrimary, BlendMode.srcIn),
        height: 20,
      ),
    );
  }

  Widget _buildTabBarView() {
    return BlocBuilder<OrderContainerCubit, OrderContainerState>(
      bloc: _cubit,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ColoredBox(
              color: vpColor.backgroundElevation0,
              child: VPTabBar(
                controller: _tabController,
                tabs: [
                  Tab(text: VPTradingLocalize.current.trading_normal),
                  Tab(
                    text: VPTradingLocalize.current.trading_transaction_history,
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                physics: const RangeMaintainingScrollPhysics(),
                children: const [NormalOrderScreen(), TransactionHistoryPage()],
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSearchOrderContainer() {
    context.pushNamed(
      VPStockCommonRouter.search.routeName,
      queryParameters:
          SearchArgs(
            marketCodes: [
              MarketCode.HOSE,
              MarketCode.HNX,
              MarketCode.UPCOM,
              MarketCode.FU,
            ],
          ).toQueryParams(),
    );
  }
}
