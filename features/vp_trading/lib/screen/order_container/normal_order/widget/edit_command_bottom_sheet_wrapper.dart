import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_bottom_sheet.dart';

/// Wrapper class that provides necessary BLoCs for EditCommandBottomSheet
class EditCommandBottomSheetWrapper extends StatelessWidget {
  final String stockCode;
  final double currentPrice;
  final int currentVolume;
  final Function(double newPrice, int newVolume) onConfirm;

  final OrderAction orderAction;
  final OrderType orderType;
  final String accountId;

  const EditCommandBottomSheetWrapper({
    super.key,
    required this.stockCode,
    required this.currentPrice,
    required this.currentVolume,
    required this.onConfirm,
    required this.accountId,
    this.orderAction = OrderAction.buy,
    this.orderType = OrderType.lo,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (context) =>
                  AvailableTradeCubit()
                    ..getAvailableTrade(accountId, stockCode),
        ),
        BlocProvider(
          create: (context) => StockInfoCubit()..loadData(stockCode),
        ),
      ],
      child: BlocBuilder<StockInfoCubit, StockInfoState>(
        builder: (context, stockState) {
          return VPLoadingBuilder(
            showLoading: stockState.status.isLoading,
            builder: (context, child) {
              if (stockState.stockInfo == null) {
                return const VPBankLoading();
              }
              return EditCommandBottomSheet(
                stockCode: stockCode,
                currentPrice: currentPrice,
                currentVolume: currentVolume,
                onConfirm: onConfirm,
                stockInfo: stockState.stockInfo!,
                orderAction: orderAction,
                orderType: orderType,
              );
            },
          );
        },
      ),
    );
  }
}
