import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/order_container/normal_order/normal_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/new_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_bottom_sheet_wrapper.dart';
import 'package:vp_trading/utils/stock_utils.dart';

import '../../../../widgets/content_expansion_widget.dart';

class OrderDetailBottomSheet extends StatefulWidget {
  const OrderDetailBottomSheet({
    super.key,
    required this.model,
    this.onEditCommand,
    this.onDeleteCommand,
  });

  final OrderBookModel model;

  final VoidCallback? onEditCommand;

  final VoidCallback? onDeleteCommand;

  @override
  State<OrderDetailBottomSheet> createState() => _OrderDetailBottomSheetState();
}

class _OrderDetailBottomSheetState extends State<OrderDetailBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return _buildContent(context);
  }

  Widget _buildContent(BuildContext context) {
    final titleStyle = vpTextStyle.subtitle14.copyColor(themeData.gray700);

    final valueStyle = vpTextStyle.subtitle14.copyColor(themeData.focus);

    final execprice = widget.model.execPrice ?? 0;

    final execqtty = widget.model.execQty ?? 0;

    final qtty = widget.model.qty ?? 0;

    final execqttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      execqtty,
    );

    final qttyFormat = AppNumberFormatUtils.shared.percentFormatter.format(
      qtty,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          /// Tiểu khoản
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_sub_account,
            value: StockUtils.getSubAccountName(widget.model.accountId),
            // value: (widget.model.producttypename ?? '').replaceAll(".", ''),
          ),

          /// Mã cổ phiếu
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_history_stock_code,
            value: widget.model.symbol ?? '',
          ),

          /// Lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_stock_type,
            value: widget.model.orderTypeEnum.title,
          ),

          /// Loại lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_command_type,
            value: widget.model.priceType ?? "-",
          ),

          /// Giá đặt lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_price,
            value: widget.model.price.toString().getPriceFormatted(),
          ),

          /// Thời gian đặt lệnh
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_time,
            value:
                "${AppTimeUtils.format(DateTime.now(), AppTimeUtilsFormat.dateNormal)} ${widget.model.tradeTime ?? ''}",
          ),

          Divider(color: themeData.divider, thickness: 1, height: 48),

          /// Khối lượng khớp
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            padding: EdgeInsets.zero,
            value: '$execqttyFormat/$qttyFormat',
            title: VPTradingLocalize.current.trading_joint_volume,
          ),

          /// Giá khớp
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            value: execprice.toDouble().getPriceFormatted(
              currency: '',
              convertToThousand: true,
            ),
            title: VPTradingLocalize.current.trading_matched_price,
          ),

          /// Giá trị lệnh đặt. Giá trị lệnh đặt = Giá đặt * KL đặt
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle,
            title: VPTradingLocalize.current.trading_order_value_title,
            value:
                ((double.tryParse(widget.model.price ?? '0') ?? 0) *
                        (widget.model.qty ?? 0))
                    .toMoney(),
          ),

          ///Trạng thái
          ContentExpansionWidget(
            titleStyle: titleStyle,
            textStyle: valueStyle?.copyWith(
              color: widget.model.newOrderStatusEnum.textColor,
            ),
            title: VPTradingLocalize.current.trading_status,
            value: widget.model.newOrderStatusEnum.label,
          ),
          const SizedBox(height: 8),

          /// build bottom actions view
          buildActionBottom(context),
        ],
      ),
    );
  }

  Widget buildActionBottom(BuildContext context) {
    if (widget.model.allowAmend == StockAppConstants.n &&
        widget.model.allowCancel == StockAppConstants.n) {
      return Padding(
        padding: const EdgeInsets.only(top: 8),
        child: VpsButton.secondarySmall(
          title: VPCommonLocalize.current.close,
          onPressed: () {
            Navigator.of(context).pop();
          },
          alignment: Alignment.center,
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: VpsButton.secondaryDangerSmall(
              title: VPTradingLocalize.current.trading_cancel_order,
              onPressed: () {
                VPPopup.outlineAndPrimaryButton(
                      title:
                          VPTradingLocalize.current.trading_cancel_order_title,
                      content:
                          VPTradingLocalize
                              .current
                              .trading_cancel_order_confirm_message,
                    )
                    .copyWith(icon: VpTradingAssets.icons.orderCancelIcon.svg())
                    .copyWith(
                      button: VpsButton.secondarySmall(
                        title: VPTradingLocalize.current.trading_close,
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    )
                    .copyWith(
                      button: VpsButton.primaryDangerSmall(
                        title:
                            VPTradingLocalize
                                .current
                                .trading_cancel_order_title,
                        onPressed: () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                          if (widget.onDeleteCommand != null) {
                            widget.onDeleteCommand!();
                          }
                        },
                      ),
                    )
                    .showDialog(context);
              },
              alignment: Alignment.center,
            ),
          ),
          // if (model.allowAmend == StockAppConstants.y) ...[
          const SizedBox(width: 8),
          Expanded(
            child: VpsButton.secondarySmall(
              title: VPTradingLocalize.current.trading_edit_order,
              onPressed: () {
                _showEditOrder(context);
                // Navigator.of(context).pop();
                // if (onEditCommand != null) {
                //   onEditCommand!();
                // }
              },
              alignment: Alignment.center,
            ),
          ),
        ],
        // ],
      ),
    );
  }

  OrderAction _getOrderActionFromSide(String? side) {
    if (side?.toLowerCase() == 'sell' || side?.toLowerCase() == 's') {
      return OrderAction.sell;
    }
    return OrderAction.buy; // Default to buy
  }

  OrderType _getOrderTypeFromPriceType(String? priceType) {
    switch (priceType?.toLowerCase()) {
      case 'lo':
      case 'limit':
        return OrderType.lo;
      case 'buyin':
      case 'buy_in':
        return OrderType.buyIn;
      case 'stoploss':
      case 'stop_loss':
        return OrderType.stopLoss;
      case 'takeprofit':
      case 'take_profit':
        return OrderType.takeProfit;
      case 'gtc':
        return OrderType.gtc;
      default:
        return OrderType.lo; // Default to limit order
    }
  }

  void _showEditOrder(BuildContext context) {
    VPPopup.bottomSheet(
      EditCommandBottomSheetWrapper(
        stockCode: widget.model.symbol ?? '',
        currentPrice: double.tryParse(widget.model.price ?? '0') ?? 0,
        currentVolume: widget.model.qty ?? 0,
        accountId: widget.model.accountId ?? '',
        orderAction: _getOrderActionFromSide(widget.model.side),
        orderType: _getOrderTypeFromPriceType(widget.model.priceType),
        onConfirm: (newPrice, newVolume) {
          _handleOrderModification(context, newPrice, newVolume);
        },
      ),
    ).showSheet(context);
  }

  /// Handle order modification with proper loading states and error handling
  void _handleOrderModification(
    BuildContext context,
    double newPrice,
    int newVolume,
  ) async {
    try {
      // Get the NormalOrderCubit from context
      final normalOrderCubit = context.read<NormalOrderCubit>();

      // Call the modify order method
      await normalOrderCubit.modifyOrder(
        orderId: widget.model.orderId ?? '',
        accountId: widget.model.accountId ?? '',
        symbol: widget.model.symbol ?? '',
        newPrice: newPrice,
        newVolume: newVolume,
      );

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Check if modification was successful
      final state = normalOrderCubit.state;
      if (state.errorMessage == null) {
        // Show success message
        this.context.showSuccess(
          content: VPTradingLocalize.current.trading_update_order_success,
        );

        // Close the bottom sheet
        Navigator.of(this.context).pop();
      } else {
        // Show error message
        this.context.showError(content: state.errorMessage!);
      }
    } catch (error) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      this.context.showError(
        content: 'Order modification failed. Please try again.',
      );
    }
  }
}
