import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/symbol_search/order_symbol_search_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/symbol_search/widget/chosen_symbol_list.dart';
import 'package:vp_trading/screen/place_order/widgets/symbol_search/widget/symbol_search_dialog.dart';

class SymbolSearchView extends StatelessWidget {
  const SymbolSearchView({super.key});
  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return BlocProvider(
      create: (_) => OrderSymbolSearchCubit(),
      child: SizedBox(
        height: 56,
        child: Row(
          children: [
            SizedBox(
              height: 32,
              child: Row(
                children: [
                  DesignAssets.icons.icSearch.svg(
                    colorFilter: ColorFilter.mode(
                      themeData.gray900,
                      BlendMode.srcIn,
                    ),

                    height: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    "Tìm mã",
                    style: vpTextStyle.captionMedium.copyColor(
                      colorUtils.gray900,
                    ),
                  ),
                  const SizedBox(width: 4),
                ],
              ),
            ),
            SizedBox(
              height: 20,
              child: VerticalDivider(color: colorUtils.gray700),
            ),
            Expanded(
              child: ChosenSymbolList(
                listStockChoosen:
                    context
                        .read<OrderSymbolSearchCubit>()
                        .state
                        .listStockChoosen,
                onChoose: (stock) {
                  //  context.read<OrderSymbolSearchCubit>().chooseStock(stock)
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<dynamic> showChooseStockBottomSheet(BuildContext context, bool isSell) {
  return VPPopup.bottomSheet(
    SymbolSearchDialog(
      isSell: isSell,
      subAccountType: context.read<PlaceOrderCubit>().state.subAccountType,
    ),
  ).showSheet(context);
}
