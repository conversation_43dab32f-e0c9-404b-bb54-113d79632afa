import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/screens/search/stock_search/widgets/search_item_shimmer_view.dart';
import 'package:vp_trading/cubit/place_order/symbol_search/order_symbol_search_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/symbol_search/widget/stock_search_item.dart';

import 'chosen_symbol_wrap.dart';

class SymbolSearchDialog extends StatefulWidget {
  const SymbolSearchDialog({
    super.key,
    required this.isSell,
    this.subAccountType,
  });
  final bool isSell;
  final SubAccountType? subAccountType;

  @override
  State<SymbolSearchDialog> createState() => _SymbolSearchDialogState();
}

class _SymbolSearchDialogState extends State<SymbolSearchDialog>
    with SingleTickerProviderStateMixin {
  final TextEditingController controller = TextEditingController();
  final scrollController = ScrollController();
  final FocusNode focusNode = FocusNode();
  late final OrderSymbolSearchCubit cubit;

  @override
  void initState() {
    super.initState();
    cubit = OrderSymbolSearchCubit(subAccountType: widget.subAccountType);
    // Request focus after the dialog is built to show cursor and keyboard
    WidgetsBinding.instance.addPostFrameCallback((_) {
      focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    controller.dispose();
    scrollController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return BlocProvider(
      create: (_) => cubit..loadData(isSell: widget.isSell),
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: Column(
          children: [
            VPTextFieldWithClear.small(
              keyboardType: TextInputType.text,
              inputType: InputType.typing,
              autofocus: true,
              onChanged: (search) {
                cubit.search(search);
              },
              controller: controller,
              focusNode: focusNode,
              hintText: "Tìm mã chứng khoán",
              prefixIcon:
                  (_) => IconButton(
                    onPressed: () {},
                    icon: DesignAssets.icons.icSearch.svg(
                      colorFilter: ColorFilter.mode(
                        colorUtils.gray900,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
            ),
            Expanded(
              child: BlocConsumer<
                OrderSymbolSearchCubit,
                OrderSymbolSearchState
              >(
                listener: (context, state) {
                  if (state.action == SearchAction.searchSuccess) {
                    if (state.filterStock.isNotEmpty) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (scrollController.hasClients) {
                          scrollController.jumpTo(0);
                        }
                      });
                    }
                  }
                },
                builder: (context, state) {
                  if (state.apiStatus.isLoading) {
                    return const StockShimmerList();
                  }
                  if (state.filterStock.isEmpty) {
                    return const NoDataView(
                      content: "Không có kết quả phù hợp",
                    );
                  }
                  return SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        if (state.listStockChoosen.isNotEmpty)
                          ChosenSymbolWrap(
                            listStockChoosen: state.listStockChoosen,
                            onChoose: (stock) {
                              cubit.updateListStockChoosen(
                                state.allStock.firstWhere(
                                  (e) => e.symbol == stock,
                                ),
                              );
                              Navigator.pop(context, stock);
                            },
                            onRemove: (stock) {
                              cubit.removeFromListStockChoosen(stock);
                            },
                          ),
                        if (state.listStockChoosen.isNotEmpty)
                          const SizedBox(height: 8),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            maxHeight: MediaQuery.of(context).size.height * 0.6,
                          ),
                          child: ListView.separated(
                            controller: scrollController,
                            shrinkWrap: true,
                            keyboardDismissBehavior:
                                ScrollViewKeyboardDismissBehavior.onDrag,
                            physics: const BouncingScrollPhysics(),
                            padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                            separatorBuilder: (_, __) => const DividerWidget(),
                            itemBuilder: (_, index) {
                              return StockSearchItem(
                                stock: state.filterStock[index],
                                onTap: () {
                                  cubit.updateListStockChoosen(
                                    state.filterStock[index],
                                  );
                                  Navigator.pop(
                                    context,
                                    state.filterStock[index].symbol,
                                  );
                                },
                              );
                            },
                            itemCount: state.filterStock.length,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
