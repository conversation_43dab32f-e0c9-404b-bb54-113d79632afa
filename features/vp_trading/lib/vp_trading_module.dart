import 'package:flutter/widgets.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/core/repository/place_order_repository.dart';
import 'package:vp_trading/core/service/command_history_service.dart';
import 'package:vp_trading/core/service/holding_portfolio_service.dart';
import 'package:vp_trading/core/service/place_order_service.dart';
import 'package:vp_trading/generated/intl/messages_all.dart';
import 'package:vp_trading/generated/l10n.dart';

import 'router/trading_router.dart';

class TradingModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton<PlaceOrderService>(
      () => PlaceOrderService(
        service(),
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    // Updated to SIT environment for order modification functionality
    service.registerLazySingleton<CommandHistoryService>(
      () => CommandHistoryService(
        service(),
        baseUrl: "https://neopro-sit.vpbanks.com.vn",
      ),
    );
    // todo cuong: sửa lại enpoint sau
    service.registerLazySingleton<HoldingPortfolioService>(
      () => HoldingPortfolioService(
        service(),
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    service.registerLazySingleton<CommandHistoryRepository>(
      () => CommandHistoryRepositoryImpl(commandHistoryService: service()),
    );
    service.registerLazySingleton<PlaceOrderRepository>(
      () => PlaceOrderRepositoryImpl(placeOrderService: service()),
    );
    service.registerLazySingleton<HoldingPortfolioRepository>(
      () => HoldingPortfolioRepositoryImpl(holdingPortfolioService: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return tradingRouter();
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return 'vpTrading';
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPTradingLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPTradingLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
