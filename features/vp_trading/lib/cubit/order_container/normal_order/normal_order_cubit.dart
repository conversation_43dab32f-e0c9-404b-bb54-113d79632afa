import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/common_paging_state/common_paging_state.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/model/request/order/update_order_request.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

part 'normal_order_state.dart';

class NormalOrderCubit extends Cubit<NormalOrderState> {
  NormalOrderCubit() : super(NormalOrderState());
  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void init() {
    emit(
      state.copyWith(
        filterParam: FilterNormalParam(
          subAccountModel: GetIt.instance<SubAccountCubit>().defaultSubAccount,
          orderStatus: [OrderStatusEnum.all],
        ),
        request: state.request.copyWith(
          accountId:
              GetIt.instance<SubAccountCubit>().defaultSubAccount.id ?? "",
          productTypeCd:
              GetIt.instance<SubAccountCubit>()
                  .defaultSubAccount
                  .productTypeCd ??
              "",
        ),
      ),
    );
    loadData();
  }

  Future<void> loadData({OrderBookRequest? queryCustom}) async {
    try {
      emit(state.copyWith(isLoading: true));

      final result = await _commandHistoryRepository.getOrder(
        queries:
            (queryCustom != null)
                ? queryCustom
                : state.request.copyWith(pageNo: 1),
      );

      final items = result.data?.content ?? [];
      final hasMore = items.isNotEmpty && items.length >= state.pageSize;

      emit(
        state.copyWith(
          isLoading: false,
          listItems: items,
          pagingState: state.pagingState.copyWith(
            currentPage: 1,
            hasMore: hasMore,
          ),
        ),
      );
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  Future<void> loadMore() async {
    if (state.isLoading || !state.hasMore) return;

    try {
      final result = await _commandHistoryRepository.getOrder(
        queries: state.request.copyWith(pageNo: state.nextPage),
      );

      final newItems = result.data?.content ?? [];
      final hasMore = newItems.isNotEmpty && newItems.length >= state.pageSize;

      emit(
        state.copyWith(
          listItems: [...state.listItems, ...newItems],
          pagingState: state.pagingState.copyWith(
            currentPage: state.nextPage,
            hasMore: hasMore,
          ),
        ),
      );
    } catch (e) {
      showError(e);
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  Future<void> updateFilterStatus(List<OrderStatusEnum> orderStatus) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(orderStatus: orderStatus),
        request: state.request.copyWith(
          orderStatus: orderStatus.map((e) => e.codeRequest).join(','),
        ),
      ),
    );
    await loadData();
  }

  Future<void> updateFilterAccount(SubAccountModel subAccountModel) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(
          subAccountModel: subAccountModel,
        ),
        request: state.request.copyWith(
          accountId: subAccountModel.id ?? "",
          productTypeCd: subAccountModel.productTypeCd ?? "",
        ),
      ),
    );
    await loadData();
  }

  Future<void> updateFilter(FilterNormalParam filterParam) async {
    final currentFilter = state.filterParam;
    final newFilter = currentFilter?.copyWith(
      orderType: filterParam.orderType,
      symbol: filterParam.symbol,
    );
    final request = state.request.copyWith(
      symbol: filterParam.symbol ?? "",
      side: filterParam.orderType?.codeRequest,
    );
    emit(state.copyWith(filterParam: newFilter, request: request));
    await loadData();
  }

  // gọi ở màn đặt lệnh, trạng thái chờ khớp
  void initForWaitingOrders(SubAccountType subAccountType) {
    var queryCustom = OrderBookRequest(
      orderStatus:
          "${OrderStatusEnum.waiting.codeRequest},${OrderStatusEnum.matchedPartial.codeRequest}",
      accountId: subAccountType.toSubAccountModel()?.id ?? "",
      productTypeCd: subAccountType.toSubAccountModel()?.productTypeCd ?? "",
    );

    loadData(queryCustom: queryCustom);
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Utility method to generate request ID
  String _generateRequestId() {
    return String.fromCharCodes(
      List.generate(20, (index) => Random().nextInt(33) + 89),
    );
  }

  /// Utility method to get current username
  String? _getCurrentUsername() {
    return GetIt.instance<AuthCubit>().userInfo?.userinfo?.username;
  }

  /// Utility method to determine market from stock symbol
  String _getMarketFromSymbol(String symbol) {
    // Default market determination logic
    // This can be enhanced based on business rules
    if (symbol.contains('HOSE') || symbol.length == 3) {
      return 'HOSE';
    } else if (symbol.contains('HNX')) {
      return 'HNX';
    } else if (symbol.contains('UPCOM')) {
      return 'UPCOM';
    }
    return 'HOSE'; // Default to HOSE
  }

  /// Modify order with new price and volume
  Future<void> modifyOrder({
    required String orderId,
    required String accountId,
    required String symbol,
    required double newPrice,
    required int newVolume,
  }) async {
    try {
      emit(state.copyWith(isLoading: true, errorMessage: null));

      final username = _getCurrentUsername();
      if (username == null) {
        throw Exception('User not authenticated');
      }

      final updateRequest = UpdateOrderRequest(
        accountId: accountId,
        orderId: orderId,
        market: _getMarketFromSymbol(symbol),
        via: 'V',
        username: username,
        requestId: _generateRequestId(),
        qty: newVolume,
        price: newPrice.toString(),
      );

      final response = await _commandHistoryRepository.editOrder(updateRequest);

      if (response.isSuccess) {
        // Automatically refresh the order list after successful modification
        await loadData();
        emit(state.copyWith(isLoading: false));
      } else {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: response.message ?? 'Order modification failed',
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(error),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }
}
