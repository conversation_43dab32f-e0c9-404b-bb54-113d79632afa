import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/utils/condition_command_util.dart';

part 'validate_order_state.dart';

enum ErrorPrice {
  outOfRange,
  step10Invalid,
  step50Invalid,
  step100Invalid,
  none,
  empty,
  init,
}

enum ErrorVolume {
  invalid,
  invalidMax,
  invalidBuy,
  invalidSell,
  none,
  empty,
  init,
}

extension ErrorPriceExtension on ErrorPrice {
  bool get isError {
    return this != ErrorPrice.none &&
        this != ErrorPrice.empty &&
        this != ErrorPrice.init;
  }

  String get message {
    switch (this) {
      case ErrorPrice.outOfRange:
        return '<PERSON><PERSON><PERSON> nhập phải nằm trong khoảng trần sàn';
      case ErrorPrice.step10Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 10 đ';
      case ErrorPrice.step50Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 50 đ';
      case ErrorPrice.step100Invalid:
        return 'Bước giá không hợp lệ phải chia hết cho 100 đ';
      case ErrorPrice.none:
        return '';
      case ErrorPrice.empty:
        return '';
      case ErrorPrice.init:
        return '';
    }
  }
}

extension ErrorVolumeExtension on ErrorVolume {
  bool get isError {
    return this != ErrorVolume.none &&
        this != ErrorVolume.empty &&
        this != ErrorVolume.init;
  }

  String message(String? value) {
    switch (this) {
      case ErrorVolume.invalid:
        return 'Khối lượng không hợp lệ';
      case ErrorVolume.invalidMax:
        return 'Vượt khối lượng tối đa là $value';
      case ErrorVolume.invalidBuy:
        return 'Vượt quá sức mua của tiểu khoản';
      case ErrorVolume.invalidSell:
        return 'Bạn không nắm giữ cổ phiếu này';
      case ErrorVolume.none:
        return '';
      case ErrorVolume.empty:
        return '';
      case ErrorVolume.init:
        return '';
    }
  }
}

class ValidateOrderCubit extends Cubit<ValidateOrderState> {
  ValidateOrderCubit() : super(const ValidateOrderState());
  StockInfoModel? _stockInfo;
  OrderType _orderType = OrderType.lo;
  OrderAction _actionType = OrderAction.buy;
  bool get isLoOrGtcOrBuyIn =>
      _orderType.isLoOrGtc || _orderType == OrderType.buyIn;
  AvailableTradeModel? _availableTrade;
  void updateParam({
    StockInfoModel? stockInfo,
    AvailableTradeModel? availableTrade,
    OrderAction? action,
    OrderType? orderType,
  }) {
    if (stockInfo != null) {
      _stockInfo = stockInfo;
    }
    if (availableTrade != null) {
      _availableTrade = availableTrade;
    }
    if (action != null) {
      _actionType = action;
    }
    if (orderType != null) {
      _orderType = orderType;
    }

    onChangePrice(state.currentPrice ?? "");
    onChangeVolumne(state.currentVolume ?? "");
  }

  void setOriginalValues({
    required String originalPrice,
    required String originalVolume,
  }) {
    emit(
      state.copyWith(
        originalPrice: originalPrice,
        originalVolume: originalVolume,
      ),
    );
  }

  void validateError({ErrorPrice? errorPrice, ErrorVolume? errorVolume}) {
    emit(state.copyWith(errorPrice: errorPrice, errorVolume: errorVolume));
  }

  void clear() {
    emit(const ValidateOrderState());
  }

  void focusField(FocusKeyboard type) {
    emit(state.copyWith(focusKeyboard: type));
  }

  void caculateValue(String value) {
    emit(state.copyWith(calculateValue: value));
  }

  void setSessionType(SessionType sessionType) {
    emit(state.copyWith(sessionType: sessionType));
  }

  void clearSession() {
    emit(state.clearSession());
  }

  double _valuePrice(String text) {
    final floor = _stockInfo?.floor ?? 0.0;
    final ceiling = _stockInfo?.ceiling ?? 0.0;
    return state.sessionType == null
        ? text.price ?? 0.0
        : _actionType == OrderAction.buy
        ? ceiling.toDouble()
        : floor.toDouble();
  }

  double _quotePrice(String text) {
    final floor = _stockInfo?.floor ?? 0.0;
    final ceiling = _stockInfo?.ceiling ?? 0.0;
    return isLoOrGtcOrBuyIn
        ? _valuePrice(text)
        : _actionType == OrderAction.buy
        ? ceiling.toDouble()
        : floor.toDouble();
  }

  double _stepPrice(String text) {
    return _stockInfo?.stockType == StockType.CW ||
            (_stockInfo?.stockType == StockType.EF &&
                _stockInfo?.exchangeCode != ExchangeCode.UPCOM)
        ? 10.0
        : _stockInfo?.exchangeCode == ExchangeCode.HOSE
        ? (text.price ?? 0.0).stepHose
        : 100.0;
  }

  priceTap({required String text, bool increase = true}) {
    if (_stockInfo == null) return '';
    final floor = _stockInfo?.floor ?? 0.0;
    final ceiling = _stockInfo?.ceiling ?? 0.0;
    if (text.isEmpty) {
      onChangePrice(
        (_actionType == OrderAction.buy ? floor : ceiling).getPriceFormatted(
          convertToThousand: true,
        ),
      );
      return;
    }
    if (text.price == 0.0 && !increase) {
      return onChangePrice(0.0.getPriceFormatted(convertToThousand: true));
    }
    final newText = ConditionCommandUtil.updateValue(
      increase,
      _valuePrice(text),
      _stepPrice(text),
    ).toDouble().getPriceFormatted(convertToThousand: true);
    final finalText = newText.length > 8 ? text : newText;
    onChangePrice(finalText);
  }

  onChangePrice(String value) {
    final error = validatePrice(text: value);
    String valueDisplay() {
      return (_quotePrice(value) * (state.currentVolume?.volume ?? 0))
          .valueText;
    }

    emit(
      state.copyWith(
        errorPrice: error,
        calculateValue: valueDisplay(),
        currentPrice: value,
      ),
    );
  }

  ErrorPrice validatePrice({required String text}) {
    if (state.sessionType != null) {
      return ErrorPrice.none;
    }

    if (_stockInfo == null) return ErrorPrice.none;

    final floor = _stockInfo!.floor ?? 0.0;
    final ceiling = _stockInfo!.ceiling ?? 0.0;
    if (text.isEmpty) {
      return ErrorPrice.empty;
    }
    if (text.price == 0.0 ||
        (_orderType.isLo &&
            (_valuePrice(text) > ceiling || _valuePrice(text) < floor))) {
      return ErrorPrice.outOfRange;
    } else if (_valuePrice(text) % _stepPrice(text) == 0) {
      return ErrorPrice.none;
    } else if (_stepPrice(text) == 10.0) {
      return ErrorPrice.step10Invalid;
    } else if (_stepPrice(text) == 50.0) {
      return ErrorPrice.step50Invalid;
    } else if (_stepPrice(text) == 100.0) {
      return ErrorPrice.step100Invalid;
    }
    return ErrorPrice.none;
  }

  double maxVolumeGtc() {
    return _stockInfo?.exchangeCode == ExchangeCode.HOSE ? 500000.0 : 1000000.0;
  }

  num getMaxVolumeByAvailableTradeResponse(AvailableTradeModel availableTrade) {
    return (_orderType.isGtc
            ? maxVolumeGtc()
            : _actionType == OrderAction.buy
            ? availableTrade.maxBuyQty
            : availableTrade.maxSellQty) ??
        0.0;
  }

  num maxVolume() {
    if (_availableTrade == null) return 0.0;
    final maxVolume = getMaxVolumeByAvailableTradeResponse(_availableTrade!);
    return maxVolume > 0.0 ? maxVolume : 0.0;
  }

  bool _invalidLot(num volume) {
    return volume.isOddLot &&
        !(_orderType.isLo && (state.sessionType?.isAtoAtc ?? true));
  }

  ErrorVolume validateVolume({required String text}) {
    if (text.isEmpty) {
      return ErrorVolume.empty;
    }
    final valueVolume = text.volume;

    if (text.volume == 0 ||
        (valueVolume >= 100 && valueVolume % 100.0 != 0.0) ||
        _invalidLot(valueVolume)) {
      return ErrorVolume.invalid;
    }
    if (maxVolume() == 0 && _actionType == OrderAction.buy) {
      return ErrorVolume.invalidBuy;
    }

    if (maxVolume() == 0 && _actionType == OrderAction.sell) {
      return ErrorVolume.invalidSell;
    }

    if (text.volume > maxVolume() && !_orderType.isGtc) {
      return ErrorVolume.invalidMax;
    }

    return ErrorVolume.none;
  }

  onChangeVolumne(String value) {
    final error = validateVolume(text: value);

    String valueDisplay() {
      return (_quotePrice(state.currentPrice ?? "") * (value.volume)).valueText;
    }

    emit(
      state.copyWith(
        errorVolume: error,
        calculateValue: valueDisplay(),
        currentVolume: value,
      ),
    );
  }

  volumneTap({required String text, bool increase = true}) {
    String onTap({required String text, bool increase = true}) {
      if (text.volume == 0.0) {
        final newText =
            maxVolume() > 0.0 && maxVolume() < 100.0
                ? maxVolume().volumeString
                : (increase ? 100.0 : 0.0).volumeString;
        return newText;
      }
      final step =
          text.volume.isOddLot && !_invalidLot(text.volume) ? 1.0 : 100.0;
      final newText =
          ConditionCommandUtil.updateValue(
            increase,
            text.volume,
            step,
          ).volumeString;
      if (newText.length > 12) return text;
      return newText;
    }

    final newText = onTap(text: text, increase: increase);
    onChangeVolumne(newText);
  }
}
