import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_stock_common/model/place_order_args.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/place_order/main/trading_order_main_screen.dart';
import 'package:vp_trading/screen/sell_portfolio/sell_portfolio_screen.dart';

enum TradingRouter {
  sellPortfolio('/sellPortfolio'),
  placeOrder('/placeOrder');

  final String routeName;

  const TradingRouter(this.routeName);
}

List<RouteBase> tradingRouter() {
  return [
    GoRoute(
      name: TradingRouter.placeOrder.routeName,
      path: TradingRouter.placeOrder.routeName,
      pageBuilder: (context, state) {
        return MaterialPage(
          key: state.pageKey,
          fullscreenDialog: true,
          child: TradingOrderMainScreen(
            argument: PlaceOrderArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
          ),
        );
      },
    ),
    GoRoute(
      path: TradingRouter.sellPortfolio.routeName,
      builder: (context, state) => const SellPortfolioScreen(),
    ),
  ];
}
