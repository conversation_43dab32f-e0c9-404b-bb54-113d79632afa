import 'package:json_annotation/json_annotation.dart';

part 'update_order_request.g.dart';

@JsonSerializable(explicitToJson: true)
class UpdateOrderRequest {
  final String? accountId;
  final String? orderId;
  final String? market;
  final String? via;
  final String? username;
  final String? requestId;
  final num? qty;
  final String? price;

  const UpdateOrderRequest({
    this.accountId,
    this.orderId,
    this.market,
    this.via,
    this.username,
    this.requestId,
    this.qty,
    this.price,
  });

  factory UpdateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateOrderRequestToJson(this);

  UpdateOrderRequest copyWith({
    String? accountId,
    String? orderId,
    String? market,
    String? via,
    String? username,
    String? requestId,
    num? qty,
    String? price,
  }) {
    return UpdateOrderRequest(
      accountId: accountId ?? this.accountId,
      orderId: orderId ?? this.orderId,
      market: market ?? this.market,
      via: via ?? this.via,
      username: username ?? this.username,
      requestId: requestId ?? this.requestId,
      qty: qty ?? this.qty,
      price: price ?? this.price,
    );
  }
}
