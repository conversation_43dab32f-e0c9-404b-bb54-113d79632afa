name: vp_core
description: A starting point for Dart libraries or applications.
version: 1.0.0
# repository: https://github.com/my_org/my_repo

environment:
  sdk: ^3.3.4

# Add regular dependencies here.
dependencies:
  flutter:
    sdk: flutter
  dio: 
  retrofit:
  # Android ID
  android_id:
  device_info_plus: 
  http:
  uuid: 
  collection:
  go_router:
  equatable:
  flutter_bloc:
  curl_logger_dio_interceptor:
  vp_common:
    path: ../vp_common
  vp_design_system:
    path: ../vp_design_system
  # Luu du lieu keychain - keystore
  flutter_secure_storage:

  firebase_core:
  firebase_analytics:
  firebase_remote_config:
  firebase_crashlytics:
  firebase_dynamic_links:
  firebase_messaging:
  firebase_database:

  json_annotation:
  flutter_localizations:
    sdk: flutter

  hive_ce: 2.11.2
  hive_ce_flutter: 2.3.1
  shared_preferences: ^2.5.3
  rxdart:
dev_dependencies:
  lints:
  test:
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  hive_ce_generator: ^1.9.1
