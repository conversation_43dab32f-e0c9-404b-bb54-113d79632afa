import 'dart:async';

import 'package:flutter/material.dart';

/// Signature for a function that defines custom position mapping for a toast
///
/// [child] is the toast widget to be positioned.
/// [gravity] is the gravity option for the toast
/// which can be used to determine the position.
/// The function should return a [Widget] that defines the position of the toast.
/// If the position is not handled by the custom logic,
/// return `null` to fall back to default logic.
typedef ToastPositionMapping = Widget? Function(
  Widget child,
  ToastGravity? gravity,
);

/// Toast Length
/// Only for Android Platform
enum Toast {
  /// Show Short toast for 1 sec
  lengthShort,

  /// Show Long toast for 5 sec
  lengthLong,
}

/// ToastGravity
/// Used to define the position of the Toast on the screen
enum ToastGravity {
  top,
  bottom,
  center,
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  centerLeft,
  centerRight,
  snackBar,
  none,
}

/// Signature for a function to buildCustom Toast
typedef PositionedToastBuilder = Widget Function(
  BuildContext context,
  Widget child,
  ToastGravity? gravity,
);

/// Runs on dart side this has no interaction with the Native Side
/// Works with all platforms just in two lines of code
/// final fToast = FToast().init(context)
/// fToast.showToast(child)
///
class VPToast {
  /// Prmary Constructor for FToast
  factory VPToast() {
    return instance;
  }

  VPToast._internal();

  static final VPToast instance = VPToast._internal();

  OverlayEntry? _entry;
  final List<_ToastEntry> _overlayQueue = [];
  Timer? _timer;
  Timer? _fadeTimer;

  /// Internal function which handles the adding
  /// the overlay to the screen
  ///
  void _showOverlay(BuildContext context) {
    if (_overlayQueue.isEmpty) {
      _entry = null;
      return;
    }

    OverlayState? overlay;
    try {
      overlay = Overlay.of(context);
    } catch (err) {
      removeQueuedCustomToasts();
    }

    if (overlay == null) return;

    /// Create entry only after all checks
    final toastEntry = _overlayQueue.removeAt(0);
    _entry = toastEntry.entry;
    overlay.insert(_entry!);

    _timer = Timer(toastEntry.duration, () {
      _fadeTimer = Timer(
        toastEntry.fadeDuration,
        () => removeCustomToast(context),
      );
    });
  }

  /// If any active toast present
  /// call removeCustomToast to hide the toast immediately
  void removeCustomToast(BuildContext context) {
    _timer?.cancel();
    _fadeTimer?.cancel();
    _timer = null;
    _fadeTimer = null;
    _entry?.remove();
    _entry = null;
    _showOverlay(context);
  }

  /// FToast maintains a queue for every toast
  /// if we called showToast for 3 times we all to queue
  /// and show them one after another
  ///
  /// call removeCustomToast to hide the toast immediately
  void removeQueuedCustomToasts() {
    _timer?.cancel();
    _fadeTimer?.cancel();
    _timer = null;
    _fadeTimer = null;
    _overlayQueue.clear();
    _entry?.remove();
    _entry = null;
  }

  /// showToast accepts all the required paramenters and prepares the child
  /// calls _showOverlay to display toast
  ///
  /// Paramenter [child] is requried
  /// toastDuration default is 2 seconds
  /// fadeDuration default is 350 milliseconds
  void showToast(
    BuildContext context, {
    required Widget child,
    PositionedToastBuilder? positionedToastBuilder,
    Duration toastDuration = const Duration(seconds: 2),
    ToastGravity? gravity,
    Duration fadeDuration = const Duration(milliseconds: 350),
    bool ignorePointer = false,
    bool isDismissible = false,
  }) {
    final Widget newChild = _ToastStateFul(
      duration: toastDuration,
      fadeDuration: fadeDuration,
      ignorePointer: ignorePointer,
      onDismiss: !isDismissible ? null : () => removeCustomToast(context),
      child: child,
    );

    /// Check for keyboard open
    /// If open will ignore the gravity bottom and change it to center
    if (gravity == ToastGravity.bottom) {
      if (MediaQuery.of(context).viewInsets.bottom != 0) {
        gravity = ToastGravity.center;
      }
    }

    final newEntry = OverlayEntry(
      builder: (context) {
        if (positionedToastBuilder != null) {
          return positionedToastBuilder(context, newChild, gravity);
        }

        return _getPositionWidgetBasedOnGravity(context, newChild, gravity);
      },
    );
    _overlayQueue.add(
      _ToastEntry(
        entry: newEntry,
        duration: toastDuration,
        fadeDuration: fadeDuration,
      ),
    );
    if (_timer == null) _showOverlay(context);
  }

  /// _getPositionWidgetBasedOnGravity generates [Positioned] [Widget]
  /// based on the gravity  [ToastGravity] provided by the user in
  /// [showToast]
  Widget _getPositionWidgetBasedOnGravity(
    BuildContext context,
    Widget child,
    ToastGravity? gravity,
  ) {
    switch (gravity) {
      case ToastGravity.top:
        return Positioned(top: 100, left: 24, right: 24, child: child);
      case ToastGravity.topLeft:
        return Positioned(top: 100, left: 24, child: child);
      case ToastGravity.topRight:
        return Positioned(top: 100, right: 24, child: child);
      case ToastGravity.center:
        return Positioned(
          top: 50,
          bottom: 50,
          left: 24,
          right: 24,
          child: child,
        );
      case ToastGravity.centerLeft:
        return Positioned(top: 50, bottom: 50, left: 24, child: child);
      case ToastGravity.centerRight:
        return Positioned(top: 50, bottom: 50, right: 24, child: child);
      case ToastGravity.bottomLeft:
        return Positioned(bottom: 50, left: 24, child: child);
      case ToastGravity.bottomRight:
        return Positioned(bottom: 50, right: 24, child: child);
      case ToastGravity.snackBar:
        return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          left: 0,
          right: 0,
          child: child,
        );
      case ToastGravity.none:
        return Positioned.fill(child: child);
      case ToastGravity.bottom:
        return Positioned(bottom: 50, left: 24, right: 24, child: child);
      case null:
        return Positioned(bottom: 50, left: 24, right: 24, child: child);
    }
  }
}

/// Simple builder method to create a [TransitionBuilder]
/// and for the use in MaterialApp builder method
TransitionBuilder vpToastBuilder() {
  return (context, child) {
    return _VPToastHolder(child: child!);
  };
}

/// Simple StatelessWidget which holds the child
/// and creates an [Overlay] to display the toast
class _VPToastHolder extends StatelessWidget {
  const _VPToastHolder({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Overlay(
      initialEntries: <OverlayEntry>[
        OverlayEntry(
          builder: (BuildContext ctx) {
            return child;
          },
        ),
      ],
    );
  }
}

/// internal class [_ToastEntry] which maintains
/// each [OverlayEntry] and [Duration] for every toast user
/// triggered
class _ToastEntry {
  _ToastEntry({
    required this.entry,
    required this.duration,
    required this.fadeDuration,
  });

  final OverlayEntry entry;
  final Duration duration;
  final Duration fadeDuration;
}

/// internal [StatefulWidget] which handles the show and hide
/// animations for [VPToast]
class _ToastStateFul extends StatefulWidget {
  const _ToastStateFul({
    required this.child,
    required this.duration,
    required this.fadeDuration,
    required this.ignorePointer,
    this.onDismiss,
  });

  final Widget child;

  final Duration duration;

  final Duration fadeDuration;

  final bool ignorePointer;

  final VoidCallback? onDismiss;

  @override
  ToastStateFulState createState() => ToastStateFulState();
}

/// State for [_ToastStateFul]
class ToastStateFulState extends State<_ToastStateFul>
    with SingleTickerProviderStateMixin {
  /// Start the showing animations for the toast
  void showIt() {
    _animationController!.forward();
  }

  /// Start the hidding animations for the toast
  void hideIt() {
    _animationController!.reverse();
    _timer?.cancel();
  }

  /// Controller to start and hide the animation
  AnimationController? _animationController;

  late Animation<dynamic> _fadeAnimation;

  Timer? _timer;

  @override
  void initState() {
    _animationController = AnimationController(
      vsync: this,
      duration: widget.fadeDuration,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeIn,
    );
    super.initState();

    showIt();
    _timer = Timer(widget.duration, hideIt);
  }

  @override
  void deactivate() {
    _timer?.cancel();
    _animationController!.stop();
    super.deactivate();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onDismiss == null ? null : () => widget.onDismiss!(),
      behavior: HitTestBehavior.translucent,
      child: IgnorePointer(
        ignoring: widget.ignorePointer,
        child: FadeTransition(
          opacity: _fadeAnimation as Animation<double>,
          child: Center(
            child: Material(color: Colors.transparent, child: widget.child),
          ),
        ),
      ),
    );
  }
}
