import 'package:flutter/foundation.dart';
import 'package:vp_core/base/local_storage/local_storage.dart';
import 'package:vp_core/vp_core.dart';

abstract final class HiveTypeKey {
  static const watchListId = 1;
  static const watchListType = 2;
  static const subAccounts = 3;
  static const subAccountType = 4;
  static const stockListType = 5;
}

abstract final class LocalStorageKey {
  static const keyAccessToken = 'key-access-token';
  static const watchlist = 'watchlist';
  static const accountIds = 'accountIds';
  static const stockList = 'stockList';
  static const watchlistSuggestion = 'watchlistSuggestion';
}

final class LocalDataService {
  LocalDataService(this.localStorage);

  final LocalStorage localStorage;

  Future<void> saveList<E>({
    required String key,
    required List<E> data,
  }) async {
    try {
      await localStorage.writeList<E>(key: key, data: data);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  Future<List<E>?> readList<E>(String key) async {
    try {
      return await localStorage.readList<E>(key: key);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return null;
    }
  }

  Future<void> saveWatchlistSuggestion(WatchlistModel? watchlist) async {
    if (watchlist == null) return;

    await localStorage.write<WatchlistModel>(
      key: LocalStorageKey.watchlistSuggestion,
      data: watchlist,
    );
  }

  Future<WatchlistModel?> getWatchlistSuggestion() {
    return localStorage.read<WatchlistModel>(
      key: LocalStorageKey.watchlistSuggestion,
    );
  }

  Future<void> saveAccessToken({required String accessToken}) async {
    await localStorage.write<String>(
      key: LocalStorageKey.keyAccessToken,
      data: accessToken,
    );
  }

  Future<String?> getAccessToken() async {
    return localStorage.read<String>(
      key: LocalStorageKey.keyAccessToken,
    );
  }

  Future<dynamic> saveWatchlist(WatchlistModel watchlist) async {
    return localStorage.write<WatchlistModel>(
      key: LocalStorageKey.watchlist,
      data: watchlist.copyWith(),
    );
  }

  Future<WatchlistModel?> getWatchlistDefault() {
    return localStorage.read<WatchlistModel>(
      key: LocalStorageKey.watchlist,
    );
  }

  Future<void> deleteWatchlistDefault() async {
    return localStorage.remove<WatchlistModel>(key: LocalStorageKey.watchlist);
  }

  Future<void> saveMarketSockList({
    required String key,
    required List<String> data,
  }) async {
    return localStorage.write<dynamic>(
      key: key,
      data: data,
    );
  }

  Future<List<String>?> getMarketStockList<T>(String key) async {
    final data = await localStorage.read<dynamic>(key: key);

    if (data is List) {
      return data.cast<String>();
    }

    return null;
  }
}
