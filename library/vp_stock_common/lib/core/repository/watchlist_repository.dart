import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/service/stock_portfolio_service.dart';
import 'package:vp_stock_common/core/service/watchlist_service.dart';
import 'package:vp_stock_common/model/request/create_watchlist_request.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

abstract class WatchlistRepository {
  Future<List<WatchlistModel>?> getWatchList();

  Future<WatchlistModel?> createWatchList(CreateWatchlistRequest request);

  Future<WatchlistModel?> updateWatchList({
    required String id,
    required String name,
    required List<String> symbols,
    DefaultWatchlistAction action = DefaultWatchlistAction.keep,
  });

  Future updateWatchlistDefault({
    required WatchlistModel watchlist,
    required DefaultWatchlistAction action,
  });

  Future<WatchlistModel?> deleteWatchlist({required String id});

  Future<WatchlistModel?> getWatchListDefault();

  Future<WatchlistModel> getWatchListDefaultForPriceBoard({
    required bool isLogin,
  });
}

class WatchListRepositoryImpl extends WatchlistRepository {
  WatchListRepositoryImpl({
    required this.localDataService,
    required this.watchListService,
    required this.stockPortfolioService,
    required this.stockCommonService,
  });

  final LocalDataService localDataService;

  final WatchListService watchListService;

  final StockPortfolioService stockPortfolioService;

  final StockCommonService stockCommonService;

  static WatchlistModel? cacheWatchlistSuggestion;

  Future<WatchlistModel> getSuggestionWatchList() async {
    if (cacheWatchlistSuggestion != null) return cacheWatchlistSuggestion!;

    var data = await stockCommonService.getSuggestedLists();

    return cacheWatchlistSuggestion = data.data!.first.copyWith(
      watchListType: WatchListType.suggest,
      name: WatchListType.suggest.label,
    );
  }

  Future<WatchlistModel?> getHoldingWatchList() async {
    final result = await stockPortfolioService.getStockPortfolio();

    final symbols = result.data?.map((e) => e.symbol).toList();

    if (symbols == null || symbols.isEmpty) return null;

    return WatchlistModel(
      id: WatchListType.holding.id,
      watchListType: WatchListType.holding,
      name: WatchListType.holding.label,
      symbols: symbols,
    );
  }

  @override
  Future<WatchlistModel> getWatchListDefaultForPriceBoard({
    required bool isLogin,
  }) async {
    final watchlistDefault = await getWatchListDefault();
    if (isLogin) {
      /// watchlist create before
      final watchLists = await getWatchList();

      final watchList = watchLists?.firstWhereOrNull(
        (e) => e.id == watchlistDefault?.id,
      );
      if (watchList != null) return watchList;
    }

    /// suggestion watchlist
    if (watchlistDefault?.isWatchlistSuggestion == true) {
      return getSuggestionWatchList();
    }

    /// holding watchlist
    if (isLogin && watchlistDefault?.isWatchlistHolding == true) {
      final holdingWatchList = await getHoldingWatchList();

      if (holdingWatchList != null && holdingWatchList.isNotEmpty) {
        return holdingWatchList;
      }
    }

    return getSuggestionWatchList();
  }

  @override
  Future<List<WatchlistModel>?> getWatchList() async {
    try {
      final data = await watchListService.getWatchList();

      return data.data
          ?.map((e) => e.copyWith(watchListType: WatchListType.create))
          .toList();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  Future<void> deleteWatchlistDefault({String? id}) async {
    try {
      if (id == null) return await localDataService.deleteWatchlistDefault();

      final watchlistDefault = await getWatchListDefault();

      if (watchlistDefault?.id == id) {
        await localDataService.deleteWatchlistDefault();
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  Future<void> setWatchlistDefault(WatchlistModel watchList) async {
    try {
      await localDataService.saveWatchlist(watchList);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<WatchlistModel?> getWatchListDefault() {
    return localDataService.getWatchlistDefault();
  }

  @override
  Future updateWatchlistDefault({
    required WatchlistModel watchlist,
    required DefaultWatchlistAction action,
  }) async {
    if (action == DefaultWatchlistAction.setAsDefault) {
      await setWatchlistDefault(watchlist);
    }

    if (action == DefaultWatchlistAction.removeDefault) {
      await deleteWatchlistDefault(id: watchlist.id);
    }
  }

  @override
  Future<WatchlistModel?> updateWatchList({
    required String id,
    required String name,
    required List<String> symbols,
    DefaultWatchlistAction action = DefaultWatchlistAction.keep,
  }) async {
    try {
      final data = await watchListService.updateWatchList(id, {
        'categoryName': name,
        'symbols': symbols,
      });

      if (data.data != null) {
        await updateWatchlistDefault(watchlist: data.data!, action: action);
      }

      return data.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<WatchlistModel?> createWatchList(
    CreateWatchlistRequest request,
  ) async {
    try {
      final data = await watchListService.createWatchList(request);

      return data.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<WatchlistModel?> deleteWatchlist({required String id}) async {
    try {
      final data = await watchListService.deleteWatchlist(id);

      await deleteWatchlistDefault(id: id);

      return data.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }
}
