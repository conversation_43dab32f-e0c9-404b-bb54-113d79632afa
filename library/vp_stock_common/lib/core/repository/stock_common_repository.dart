import 'package:flutter/foundation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/enum/cord_bond_type.dart';
import 'package:vp_stock_common/model/fu_top_10_price_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

abstract class StockCommonRepository {
  Future<BaseResponse<bool>> getEODStatus();

  Future<List<StockInfoModel>?> getStockInfoBySymbols(List<String> symbols);

  Future<List<StockModel>?> getAllStock({bool loadCache = true});

  Future<BasePagingResponse<StockModel>?> getStockList({
    List<MarketCode>? marketCodes,
    int? pageNo,
    int? pageSize,
  });

  Future<BasePagingResponse<StockInfoModel>?> getMarketList({
    required MarketCode marketCode,
    required StockType stockType,
    int? pageNo,
    int? pageSize,
  });

  Future<List<String>?> getMarketListCache({
    required MarketCode marketCode,
    required StockType stockType,
  });

  Future<BasePagingResponse<StockInfoModel>?> getStockDetail({
    required bool isOddLot,
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  });

  Future<BasePagingResponse<StockInfoModel>?> getStockInfoV2({
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  });

  Future<BasePagingResponse<StockInfoModel>?> getFuStockDetail({
    List<String>? symbols,
    StockType? stockType,
    int? pageNo,
    int? pageSize,
  });

  Future<BasePagingResponse<StockInfoModel>?> getOddLotStockDetail({
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  });

  Future<WatchlistModel?> getSuggestedLists();

  Future<BasePagingResponse<StockInfoModel>?> getStocksByIndex({
    IndexCode? indexCode,
    int? pageNo,
    int? pageSize,
  });

  Future<MarketInfoModel?> getMarketInfo(IndexCode indexCode);

  Future<List<BondModel>?> getCorpBondList({CorpBondType? type});

  Future<List<FuTop10PriceModel>?> getFuTop10Price({required String symbol});
}

class StockCommonRepositoryImpl extends StockCommonRepository {
  final StockCommonService stockCommonService;

  final LocalDataService localDataService;

  StockCommonRepositoryImpl({
    required this.stockCommonService,
    required this.localDataService,
  });

  @override
  Future<BaseResponse<bool>> getEODStatus() async {
    try {
      return stockCommonService.getEODStatus();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<StockInfoModel>?> getStockInfoBySymbols(
    List<String> symbols,
  ) async {
    try {
      final response = await stockCommonService.getStockInfoBySymbols(
        symbols.join(','),
      );

      return response.data?.symbols;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<StockModel>?> getAllStock({bool loadCache = true}) async {
    try {
      /// load stocks from cache
      if (loadCache) {
        final stocks = await localDataService.readList<StockModel>(
          LocalStorageKey.stockList,
        );

        if (stocks.hasData) return stocks;
      }

      final response = await stockCommonService.getStockList(
        marketCode: 'ALL',
        pageSize: 10000,
      );

      /// save stocks to cache
      final data = response.data?.content;

      if (data.hasData) {
        await localDataService.saveList(
          key: LocalStorageKey.stockList,
          data: data!,
        );
      }

      return data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockModel>?> getStockList({
    List<MarketCode>? marketCodes,
    int? pageNo,
    int? pageSize,
  }) async {
    try {
      final response = await stockCommonService.getStockList(
        marketCode: marketCodes?.map((e) => e.name).join(','),
        pageNo: pageNo,
        pageSize: pageSize,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getStockInfoV2({
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  }) async {
    try {
      final response = await stockCommonService.getStockInfoV2(
        marketCode: marketCode?.name,
        symbols: symbols?.join(','),
        stockType: stockType?.name,
        issuerName: issuerName,
        pageNo: pageNo,
        pageSize: pageSize,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getOddLotStockDetail({
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  }) async {
    try {
      final response = await stockCommonService.getOddLotStockDetail(
        marketCode: marketCode?.name,
        symbols: symbols?.join(','),
        stockType: stockType?.name,
        issuerName: issuerName,
        pageNo: pageNo,
        pageSize: pageSize,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<WatchlistModel?> getSuggestedLists() async {
    try {
      final response = await stockCommonService.getSuggestedLists();

      final watchlist = response.data.firstOrNull?.copyWith(
        watchListType: WatchListType.suggest,
        name: WatchListType.suggest.label,
      );

      await localDataService.saveWatchlistSuggestion(watchlist);

      return watchlist;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      final cache = await localDataService.getWatchlistSuggestion();
      if (cache != null) return cache;

      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getStocksByIndex({
    IndexCode? indexCode,
    int? pageNo,
    int? pageSize,
  }) async {
    try {
      final response = await stockCommonService.getStocksByIndex(
        indexCode: indexCode?.name,
        pageNo: pageNo,
        pageSize: pageSize,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getMarketList({
    required MarketCode marketCode,
    required StockType stockType,
    int? pageNo,
    int? pageSize,
  }) async {
    final response = await getStockInfoV2(
      marketCode: marketCode,
      stockType: stockType,
      pageSize: pageSize,
      pageNo: pageNo,
    );

    final items = response?.content;

    if (items != null && items.isNotEmpty) {
      localDataService
          .saveMarketSockList(
            key: '${marketCode.name}-${stockType.name}',
            data: items.map((e) => e.symbol).toList(),
          )
          .catchError(
            (e, stackTrace) => debugPrintStack(stackTrace: stackTrace),
          );
    }

    return response;
  }

  @override
  Future<List<String>?> getMarketListCache({
    required MarketCode marketCode,
    required StockType stockType,
  }) async {
    try {
      final data = await localDataService.getMarketStockList(
        '${marketCode.name}-${stockType.name}',
      );

      return data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getStockDetail({
    required bool isOddLot,
    MarketCode? marketCode,
    List<String>? symbols,
    StockType? stockType,
    String? issuerName,
    int? pageNo,
    int? pageSize,
  }) {
    if (isOddLot) {
      return getOddLotStockDetail(
        marketCode: marketCode,
        symbols: symbols,
        stockType: stockType,
        issuerName: issuerName,
        pageSize: pageSize,
        pageNo: pageNo,
      );
    }

    return getStockInfoV2(
      marketCode: marketCode,
      symbols: symbols,
      stockType: stockType,
      issuerName: issuerName,
      pageSize: pageSize,
      pageNo: pageNo,
    );
  }

  @override
  Future<MarketInfoModel?> getMarketInfo(IndexCode indexCode) async {
    try {
      final response = await stockCommonService.getMarketInfo(indexCode.value);

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<BondModel>?> getCorpBondList({CorpBondType? type}) async {
    try {
      final response = await stockCommonService.getCorpBondList(
        type: type?.value,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BasePagingResponse<StockInfoModel>?> getFuStockDetail({
    List<String>? symbols,
    StockType? stockType,
    int? pageNo,
    int? pageSize,
  }) async {
    try {
      final response = await stockCommonService.getFuStockDetail(
        symbols: symbols?.join(','),
        stockType: stockType?.name,
        pageNo: pageNo,
        pageSize: pageSize,
      );

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<FuTop10PriceModel>?> getFuTop10Price({
    required String symbol,
  }) async {
    try {
      final response = await stockCommonService.getFuTop10Price(symbol: symbol);

      return response.data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }
}
