import 'package:vp_stock_common/model/enum/market_code.dart';
import 'package:vp_stock_common/model/stock_model.dart';

import 'package:vp_stock_common/gen/from_query_params.dart';

part 'search_args.g.dart';

enum SearchItemAction { openDetail, pickAndReturn }

@GoRouterParams()
class SearchArgs {
  SearchArgs({
    this.hint,
    this.marketCodes = MarketCode.values,
    this.itemAction = SearchItemAction.openDetail,
  });

  final String? hint;

  final List<MarketCode> marketCodes;

  final SearchItemAction itemAction;
}

class SearchExtraArgs {
  SearchExtraArgs({this.onAddSymbol});

  final Function(StockModel)? onAddSymbol;
}
