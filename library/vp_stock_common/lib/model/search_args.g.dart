// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension SearchArgsExtensions on SearchArgs {
  static SearchArgs fromQueryParams(Map<String, String> params) {
    return SearchArgs(
      hint: params['hint'],
      marketCodes:
          _tryParseEnumListMarketCode(params['marketCodes']) ??
          MarketCode.values,
      itemAction:
          _tryParseEnumSearchItemAction(params['itemAction']) ??
          SearchItemAction.openDetail,
    );
  }

  Map<String, String> toQueryParams() {
    return {
      if (hint != null) 'hint': hint!.toString(),
      'marketCodes': marketCodes.map((e) => e.name).join(','),
      'itemAction': itemAction.name,
    };
  }

  static MarketCode? _tryParseEnumMarketCode(String? name) {
    if (name == null) return null;
    return MarketCode.values.cast<MarketCode?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }

  static List<MarketCode>? _tryParseEnumListMarketCode(String? raw) {
    if (raw == null) return null;
    return raw
        .split(',')
        .map((e) => _tryParseEnumMarketCode(e))
        .whereType<MarketCode>()
        .toList();
  }

  static SearchItemAction? _tryParseEnumSearchItemAction(String? name) {
    if (name == null) return null;
    return SearchItemAction.values.cast<SearchItemAction?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }
}
