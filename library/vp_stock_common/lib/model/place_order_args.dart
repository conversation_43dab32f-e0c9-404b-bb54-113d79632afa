import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/gen/from_query_params.dart';
import 'package:vp_stock_common/model/enum/order_action.dart';

part 'place_order_args.g.dart';

@GoRouterParams()
class PlaceOrderArgs {
  final SubAccountType? subAccountType;

  final OrderAction action;

  final String symbol;

  final num? price;

  PlaceOrderArgs({
    this.symbol = 'VPB',
    this.action = OrderAction.buy,
    this.subAccountType,
    this.price,
  });
}
