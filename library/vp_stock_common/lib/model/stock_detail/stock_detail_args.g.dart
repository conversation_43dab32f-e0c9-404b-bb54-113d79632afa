// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_detail_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension StockDetailArgsExtensions on StockDetailArgs {
  static StockDetailArgs fromQueryParams(Map<String, String> params) {
    return StockDetailArgs(
      symbol: params['symbol']!,
      initialTab:
          _tryParseEnumStockDetailTab(params['initialTab']) ??
          StockDetailTab.trading,
    );
  }

  Map<String, String> toQueryParams() {
    return {'symbol': symbol.toString(), 'initialTab': initialTab.name};
  }

  static StockDetailTab? _tryParseEnumStockDetailTab(String? name) {
    if (name == null) return null;
    return StockDetailTab.values.cast<StockDetailTab?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }
}
