// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place_order_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension PlaceOrderArgsExtensions on PlaceOrderArgs {
  static PlaceOrderArgs fromQueryParams(Map<String, String> params) {
    return PlaceOrderArgs(
      symbol: params['symbol'] ?? 'VPB',
      action: _tryParseEnumOrderAction(params['action']) ?? OrderAction.buy,
      subAccountType: _tryParseEnumSubAccountType(params['subAccountType']),
      price: num.tryParse(params['price'] ?? ''),
    );
  }

  Map<String, String> toQueryParams() {
    return {
      if (subAccountType != null) 'subAccountType': subAccountType!.name,
      'action': action.name,
      'symbol': symbol.toString(),
      if (price != null) 'price': price!.toString(),
    };
  }

  static OrderAction? _tryParseEnumOrderAction(String? name) {
    if (name == null) return null;
    return OrderAction.values.cast<OrderAction?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }

  static SubAccountType? _tryParseEnumSubAccountType(String? name) {
    if (name == null) return null;
    return SubAccountType.values.cast<SubAccountType?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }
}
