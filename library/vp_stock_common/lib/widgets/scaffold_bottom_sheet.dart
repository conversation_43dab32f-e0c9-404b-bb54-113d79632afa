import 'package:flutter/material.dart';

class ScaffoldBottomSheet extends StatelessWidget {
  const ScaffoldBottomSheet({
    required this.child,
    this.heightFactor = 0.8,
    super.key,
  });

  final Widget child;

  final double heightFactor;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.sizeOf(context).height * heightFactor,
      ),
      child: child,
    );
  }
}
