import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/confirm_dialog.dart';

StockCommonNavigator get stockCommonNavigator =>
    GetIt.instance.get<StockCommonNavigator>();

abstract class StockCommonNavigator {
  Future showCreateNewWatchlistDialog(BuildContext context);

  Future openBondSearchPage(BuildContext context, {String? hint});

  Future openSearchPage(BuildContext context, {SearchArgs? args});

  Future openPriceBoardPage(BuildContext context);

  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
  });

  Future showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    ButtonType acceptButtonType = ButtonType.primary,
    ButtonDangerType acceptButtonDangerType = ButtonDangerType.defaultType,
  });

  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  });
}

class StockCommonNavigatorImpl extends StockCommonNavigator {
  @override
  Future showCreateNewWatchlistDialog(BuildContext context) {
    return VPPopup.custom(
      child: const CreateNewWatchlistDialog(),
      padding: const EdgeInsets.all(20),
    ).showDialog(context);
  }

  @override
  Future openBondSearchPage(BuildContext context, {String? hint}) {
    return context.push(VPStockCommonRouter.bondSearch.routeName);
  }

  @override
  Future openSearchPage(BuildContext context, {SearchArgs? args}) {
    return context.pushNamed(
      VPStockCommonRouter.search.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
    );
  }

  @override
  Future showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    Function? onClose,
    Function? onConfirm,
    ButtonType acceptButtonType = ButtonType.primary,
    ButtonDangerType acceptButtonDangerType = ButtonDangerType.defaultType,
  }) {
    return VPPopup.custom(
      child: ConfirmDialog(
        title: title,
        content: content,
        onClose: onClose,
        onConfirm: onConfirm,
        acceptButtonType: acceptButtonType,
        acceptButtonDangerType: acceptButtonDangerType,
      ),
      padding: const EdgeInsets.all(20),
    ).showDialog(context);
  }

  @override
  void openSignInRequiredDialog(
    BuildContext context, {
    VoidCallback? onSignInSuccess,
  }) {
    VPPopup.custom(
      child: SignInRequiredDialog(
        onSignIn:
            () => context.push(
              VPStockCommonRouter.signIn.routeName,
              extra: SignInArgs(onSignInSuccess: onSignInSuccess),
            ),
      ),
    ).showDialog(context);
  }

  @override
  Future openPriceBoardPage(BuildContext context) {
    return context.push(VPStockCommonRouter.priceBoard.routeName);
  }

  @override
  Future openStockDetailPage(
    BuildContext context, {
    required StockDetailArgs args,
  }) {
    return context.pushNamed(
      VPStockCommonRouter.stockDetail.routeName,
      queryParameters: args.toQueryParams(),
    );
  }
}
