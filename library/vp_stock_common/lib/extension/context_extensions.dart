import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

extension ContextExtensions on BuildContext {
  void showSnackBar({
    required VPSnackBarType snackBarType,
    required String content,
  }) {
    VPToast().showToast(
      this,
      child: VpSnackBarView(snackBarType: snackBarType, content: content),
    );
  }

  void showSuccess({required String content}) {
    showSnackBar(snackBarType: VPSnackBarType.success, content: content);
  }

  void showError({required String content}) {
    showSnackBar(snackBarType: VPSnackBarType.error, content: content);
  }
}
