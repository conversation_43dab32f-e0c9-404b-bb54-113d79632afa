import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/screens/search/stock_search/search_cubit.dart';
import 'package:vp_stock_common/screens/search/stock_search/widgets/search_item_shimmer_view.dart';
import 'package:vp_stock_common/screens/search/stock_search/widgets/search_item_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({this.args, this.extraArgs, super.key});

  final SearchArgs? args;

  final SearchExtraArgs? extraArgs;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SearchCubit>(
      create: (_) => SearchCubit(marketCodes: args?.marketCodes)..loadData(),
      child: _SearchBody(args: args, extraArgs: extraArgs),
    );
  }
}

class _SearchBody extends StatefulWidget {
  const _SearchBody({this.args, this.extraArgs});

  final SearchArgs? args;

  final SearchExtraArgs? extraArgs;

  @override
  State<_SearchBody> createState() => _SearchBodyState();
}

class _SearchBodyState extends State<_SearchBody> {
  final controller = TextEditingController();

  final scrollController = ScrollController();

  String? get hint => widget.args?.hint;

  bool get isSearching => controller.text.trim().isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return VPScaffold(
      backgroundColor: vpColor.backgroundElevationMinus1,
      appBar: VPAppBar.search(
        controller: controller,
        searchHint: hint ?? 'Nhập mã tìm kiếm',
        height: kToolbarHeight + 16,
        titlePadding: const EdgeInsets.only(right: 16),
        onChanged: (text) => context.read<SearchCubit>().search(text),
      ),

      body: BlocConsumer<SearchCubit, SearchState>(
        listener: (_, state) {
          if (state.action == SearchAction.searchSuccess) {
            if (scrollController.hasClients) scrollController.jumpTo(0);
          }
        },
        builder: (_, state) {
          return VPStatusBuilder(
            builder: (_, __) {
              if (state.filterStock.isEmpty) {
                if (isSearching) {
                  return const NoDataView(
                    content: 'Không có kết quả nào phù hợp',
                  );
                }

                return const NoDataView();
              }

              return ListView.separated(
                controller: scrollController,
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                separatorBuilder: (_, __) => const SizedBox(height: 4),
                itemBuilder: (_, index) {
                  return SearchItemView(
                    stock: state.filterStock[index],
                    onAddSymbol: widget.extraArgs?.onAddSymbol,
                    onTap: (stock) => onItemTap(stock),
                  );
                },
                itemCount: state.filterStock.length,
              );
            },
            loadingBuilder: (_) => const StockShimmerList(),
            apiStatus: state.apiStatus,
          );
        },
      ),
    );
  }

  void onItemTap(StockModel stock) {
    if (widget.args?.itemAction == SearchItemAction.pickAndReturn) {
      return Navigator.pop(context, stock);
    }

    Navigator.pop(context, stock);

    stockCommonNavigator.openStockDetailPage(
      context,
      args: StockDetailArgs(symbol: stock.symbol),
    );
  }

  @override
  void dispose() {
    controller.dispose();
    scrollController.dispose();
    super.dispose();
  }
}
