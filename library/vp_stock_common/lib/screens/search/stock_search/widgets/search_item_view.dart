import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

class SearchItemView extends StatelessWidget {
  const SearchItemView({
    required this.stock,
    this.onTap,
    this.onAddSymbol,
    super.key,
  });

  final StockModel stock;

  final ValueChanged<StockModel>? onTap;

  final ValueChanged<StockModel>? onAddSymbol;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap?.call(stock),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        decoration: BoxDecoration(
          color: vpColor.backgroundElevation0,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        stock.symbol,
                        style: vpTextStyle.subtitle14.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),

                      const SizedBox(width: 8),

                      Text(
                        stock.marketCode,
                        style: vpTextStyle.captionMedium.copyColor(
                          vpColor.textDisabled,
                        ),
                      ),
                    ],
                  ),

                  Text(
                    stock.stockName,
                    style: vpTextStyle.captionMedium.copyColor(
                      vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
            ),

            if (onAddSymbol != null)
              IconButton(
                onPressed: () => onAddSymbolToWatchlist(context),
                icon: Icon(Icons.add, color: vpColor.iconPrimary),
              ),
          ],
        ),
      ),
    );
  }

  void onAddSymbolToWatchlist(BuildContext context) {
    if (!isLoggedIn) {
      return stockCommonNavigator.openSignInRequiredDialog(context);
    }

    onAddSymbol?.call(stock);
  }
}
