import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:flutter/foundation.dart';

part 'search_state.dart';

class SearchCubit extends AppCubit<SearchState>
    with StockFilterMixin, StockSortMixin {
  SearchCubit({StockCommonRepository? repository, this.marketCodes})
    : stockCommonRepository = repository ?? GetIt.instance.get(),
      super(SearchState(apiStatus: ApiStatus.initial()));

  final List<MarketCode>? marketCodes;

  final StockCommonRepository stockCommonRepository;

  final debouncer = Debouncer(delay: Duration(milliseconds: 300));

  void loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      var stocks = await stockCommonRepository.getAllStock(loadCache: true);

      if (marketCodes != null && marketCodes!.isNotEmpty) {
        stocks = stocks.findByMarketCodes(marketCodes!);
      }

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          allStock: stocks,
          filterStock: stocks,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  /// Hàm tìm kiếm
  void search(String keyword) {
    debouncer.call(() {
      final stocks = searchAndSortStocks(
        searchValue: keyword.trim(),
        allStocks: state.allStock,
      );

      emit(
        state.copyWith(filterStock: stocks, action: SearchAction.searchSuccess),
      );
    });
  }

  @override
  Future<void> close() async {
    debouncer.cancel();
    super.close();
  }
}
