import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/watchlist/add_symbols_to_watchlist/add_symbols_to_watchlist_bloc.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'widgets/stocks_view.dart';

part 'widgets/bottom_action_view.dart';

part 'widgets/stock_item_view.dart';

part 'widgets/input_view.dart';

class AddSymbolsToWatchListBottomSheet extends StatelessWidget {
  const AddSymbolsToWatchListBottomSheet({
    required this.watchlist,
    this.stockTypes,
    super.key,
  });

  final WatchlistModel watchlist;

  final List<StockType>? stockTypes;

  @override
  Widget build(BuildContext context) {
    return ScaffoldBottomSheet(
      child: <PERSON><PERSON>rovider(
        create:
            (_) => AddSymbolsToWatchlistBloc(
              watchlist: watchlist,
              stockTypes: stockTypes,
            )..getStocks(),
        child: MultiBlocListener(
          listeners: [
            BlocListener<AddSymbolsToWatchlistBloc, AddSymbolsToWatchlistState>(
              listenWhen: (_, state) => state.addSymbolStatus.isDone,
              listener: (context, state) {
                addSymbolsSuccess(context, state.watchlist);
              },
            ),
            BlocListener<AddSymbolsToWatchlistBloc, AddSymbolsToWatchlistState>(
              listenWhen: (_, state) => state.addSymbolStatus.isError,
              listener: (context, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<AddSymbolsToWatchlistBloc, AddSymbolsToWatchlistState>(
              listenWhen: (_, state) => state.apiStatus.isError,
              listener: (context, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
          ],
          child: const _AddSymbolsToWatchListBottomSheet(),
        ),
      ),
    );
  }

  void addSymbolsSuccess(BuildContext context, WatchlistModel watchlist) {
    context.showSuccess(content: 'Đã thêm vào danh sách');

    context.read<WatchlistBloc>().update(watchlist);

    Navigator.pop(context);
  }
}

class _AddSymbolsToWatchListBottomSheet extends StatelessWidget {
  const _AddSymbolsToWatchListBottomSheet();

  @override
  Widget build(BuildContext context) {
    return VPLoadingBuilder(
      showLoading: context.select<AddSymbolsToWatchlistBloc, bool>(
        (bloc) => bloc.state.showLoading,
      ),
      builder: (_, child) => child!,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            SizedBox(height: 5),

            InputView(),

            SizedBox(height: 16),

            Expanded(child: StocksView()),

            SizedBox(height: 16),

            BottomActionView(),
          ],
        ),
      ),
    );
  }
}
