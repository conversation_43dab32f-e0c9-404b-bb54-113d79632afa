import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/watchlist/add_symbol_to_watchlist_selector/add_symbol_to_watchlist_selector_bloc.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

part 'widgets/watchlist_created_view.dart';

part 'widgets/add_to_watchlist_button_view.dart';

part 'widgets/new_watchlist_button_view.dart';

class AddSymbolToWatchListBottomSheet extends StatelessWidget {
  const AddSymbolToWatchListBottomSheet({required this.symbols, super.key});

  final List<String> symbols;

  @override
  Widget build(BuildContext context) {
    return ScaffoldBottomSheet(
      child: Bloc<PERSON>rovider<AddSymbolToWatchlistBloc>(
        create:
            (_) => AddSymbolToWatchlistBloc(
              symbols: symbols,
              watchlists: context.read<WatchlistBloc>().state.watchlists,
            ),
        child: MultiBlocListener(
          listeners: [
            BlocListener<WatchlistBloc, WatchlistState>(
              listenWhen: (preState, state) => state.apiStatus.isError,
              listener: (context, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<AddSymbolToWatchlistBloc, AddSymbolToWatchlistState>(
              listenWhen: (_, state) => state.apiStatus.isError,
              listener: (context, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<WatchlistBloc, WatchlistState>(
              listenWhen:
                  (pState, state) => pState.watchlists != state.watchlists,
              listener: (context, state) {
                context.read<AddSymbolToWatchlistBloc>().updateWatchlists(
                  state.watchlists,
                );
              },
            ),
            BlocListener<AddSymbolToWatchlistBloc, AddSymbolToWatchlistState>(
              listenWhen: (_, state) => state.apiStatus.isDone,
              listener: (context, state) {
                onAddSymbolsSuccess(context, state.watchlistSelected);
              },
            ),
          ],
          child: const _AddSymbolToWatchListBody(),
        ),
      ),
    );
  }

  void onAddSymbolsSuccess(BuildContext context, WatchlistModel? watchlist) {
    context.read<WatchlistBloc>().update(watchlist);

    context.showSuccess(content: 'Đã thêm ${symbols.first} vào danh sách');

    Navigator.pop(context, watchlist);
  }
}

class _AddSymbolToWatchListBody extends StatelessWidget {
  const _AddSymbolToWatchListBody();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddSymbolToWatchlistBloc, AddSymbolToWatchlistState>(
      builder: (context, state) {
        return VPLoadingBuilder(
          showLoading: state.apiStatus.isLoading,
          builder: (context, child) => child!,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Thêm vào danh sách',
                  style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
                ),

                const SizedBox(height: 16),

                const Flexible(child: _WatchlistCreatedView()),

                const VPDividerView(),

                const _NewWatchlistButtonView(),

                const SizedBox(height: 24),

                const _AddToWatchlistButtonView(),
              ],
            ),
          ),
        );
      },
    );
  }
}
