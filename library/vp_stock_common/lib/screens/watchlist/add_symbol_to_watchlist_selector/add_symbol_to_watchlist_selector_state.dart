part of 'add_symbol_to_watchlist_selector_bloc.dart';

class AddSymbolToWatchlistState {
  const AddSymbolToWatchlistState({
    required this.symbols,
    required this.apiStatus,
    this.watchlistSelected,
  });

  final List<String> symbols;

  final WatchlistModel? watchlistSelected;

  final ApiStatus apiStatus;

  AddSymbolToWatchlistState copyWith({
    ApiStatus? apiStatus,
    List<String>? symbols,
    WatchlistModel? watchlistSelected,
  }) {
    return AddSymbolToWatchlistState(
      apiStatus: apiStatus ?? this.apiStatus,
      symbols: symbols ?? this.symbols,
      watchlistSelected: watchlistSelected ?? this.watchlistSelected,
    );
  }
}
