import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/repository/watchlist_repository.dart';
import 'package:flutter/foundation.dart';

part 'add_symbol_to_watchlist_selector_state.dart';

class AddSymbolToWatchlistBloc extends AppCubit<AddSymbolToWatchlistState> {
  AddSymbolToWatchlistBloc({
    required List<String> symbols,
    List<WatchlistModel>? watchlists,
  }) : super(
         AddSymbolToWatchlistState(
           symbols: symbols,
           apiStatus: ApiStatus.initial(),
           watchlistSelected: watchlists?.firstOrNull,
         ),
       );

  final WatchlistRepository watchlistRepository =
      GetIt.instance.get<WatchlistRepository>();

  void updateWatchlists(List<WatchlistModel>? watchlists) {
    final watchlist = state.watchlistSelected ?? watchlists?.firstOrNull;

    emit(state.copyWith(watchlistSelected: watchlist));
  }

  void onWatchlistChanged(WatchlistModel watchlist) {
    emit(state.copyWith(watchlistSelected: watchlist));
  }

  Future updateWatchList() async {
    if (state.watchlistSelected == null) return;

    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final newWatchList = await watchlistRepository.updateWatchList(
        id: state.watchlistSelected!.id,
        name: state.watchlistSelected!.name,
        symbols:
            {...state.watchlistSelected!.symbols, ...state.symbols}.toList(),
      );

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          watchlistSelected: newWatchList,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }
}
