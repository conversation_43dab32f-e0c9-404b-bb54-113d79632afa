import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_selector_bottom_sheet/watchlist_selector_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

part 'widgets/watchlist_selector_item_view.dart';

part 'widgets/watchlist_created_view.dart';

class WatchlistSelectorBottomSheet extends StatelessWidget {
  const WatchlistSelectorBottomSheet({
    this.showAddWatchlistButton = false,
    this.showSuggestionWatchlist = true,
    this.showHoldingWatchlist = true,
    this.onAuthCallback,
    super.key,
  });

  final bool showAddWatchlistButton;

  final bool showSuggestionWatchlist;

  final bool showHoldingWatchlist;

  final VoidCallback? onAuthCallback;

  @override
  Widget build(BuildContext context) {
    return ScaffoldBottomSheet(
      child: BlocProvider<WatchlistSelectorBloc>(
        create: (_) => WatchlistSelectorBloc(),
        child: MultiBlocListener(
          listeners: [
            BlocListener<WatchlistSelectorBloc, WatchlistSelectorState>(
              listenWhen: (_, state) => state.suggestionWatchlist != null,
              listener: (_, state) => context.pop(state.suggestionWatchlist),
            ),

            BlocListener<WatchlistSelectorBloc, WatchlistSelectorState>(
              listenWhen: (preState, state) => state.holdingWatchlist != null,
              listener: (_, state) => context.pop(state.holdingWatchlist),
            ),

            BlocListener<WatchlistSelectorBloc, WatchlistSelectorState>(
              listenWhen: (_, state) => state.apiStatus.isError,
              listener: (_, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),

            BlocListener<WatchlistBloc, WatchlistState>(
              listenWhen: (_, state) => state.apiStatus.isError,
              listener: (_, state) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
          ],
          child: _WatchlistSelectorBody(
            showAddWatchlistButton: showAddWatchlistButton,
            showSuggestionWatchlist: showSuggestionWatchlist,
            showHoldingWatchlist: showHoldingWatchlist,
            onAuthCallback: onAuthCallback,
          ),
        ),
      ),
    );
  }
}

class _WatchlistSelectorBody extends StatelessWidget {
  const _WatchlistSelectorBody({
    this.showAddWatchlistButton = false,
    this.showSuggestionWatchlist = true,
    this.showHoldingWatchlist = true,
    this.onAuthCallback,
  });

  final bool showAddWatchlistButton;

  final bool showSuggestionWatchlist;

  final bool showHoldingWatchlist;

  final VoidCallback? onAuthCallback;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WatchlistSelectorBloc, WatchlistSelectorState>(
      buildWhen: (preState, state) => preState.apiStatus != state.apiStatus,
      builder: (context, state) {
        return VPLoadingBuilder(
          showLoading: state.apiStatus.isLoading,
          builder: (_, child) => child!,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: ColoredBox(
              color: vpColor.backgroundElevation1,
              child: CustomColumn(
                separatorBuilder: (_) => const VPDividerView(),
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (showHoldingWatchlist)
                    WatchlistSelectorItemView(
                      name: WatchListType.holding.label,
                      onTap: () => onSelectHoldingWatchlist(context),
                    ),

                  if (showSuggestionWatchlist)
                    WatchlistSelectorItemView(
                      name: WatchListType.suggest.label,
                      onTap:
                          () =>
                              context
                                  .read<WatchlistSelectorBloc>()
                                  .getSuggestionWatchlist(),
                    ),

                  const Flexible(child: WatchlistCreatedView()),

                  if (showAddWatchlistButton)
                    WatchlistSelectorItemView(
                      name: '+ Thêm danh sách',
                      color: vpColor.textBrand,
                      onTap: () => onCreateWatchlist(context),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void onSelectHoldingWatchlist(BuildContext context) {
    if (!isLoggedIn) {
      return stockCommonNavigator.openSignInRequiredDialog(
        context,
        onSignInSuccess: onAuthCallback,
      );
    }

    context.read<WatchlistSelectorBloc>().getHoldingWatchlist();
  }

  void onCreateWatchlist(BuildContext context) {
    if (!isLoggedIn) {
      return stockCommonNavigator.openSignInRequiredDialog(
        context,
        onSignInSuccess: onAuthCallback,
      );
    }

    stockCommonNavigator.showCreateNewWatchlistDialog(context);
  }
}
