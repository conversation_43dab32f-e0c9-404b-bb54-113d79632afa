import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/repository/watchlist_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'watchlist_detail_state.dart';

class WatchlistDetailBloc extends AppCubit<WatchlistDetailState> {
  WatchlistDetailBloc({required WatchlistModel watchlist})
    : super(
        WatchlistDetailState(
          apiStatus: ApiStatus.initial(),
          updateWatchlistStatus: ApiStatus.initial(),
          deleteWatchlistStatus: ApiStatus.initial(),
          watchlistName: watchlist.name,
          watchlist: watchlist,
        ),
      );

  final WatchlistRepository watchlistRepository =
      GetIt.instance.get<WatchlistRepository>();

  final StockCommonRepository stockCommonRepository =
      GetIt.instance.get<StockCommonRepository>();

  DefaultWatchlistAction get watchlistAction => switch (state
      .isDefaultWatchlist) {
    null => DefaultWatchlistAction.keep,
    true => DefaultWatchlistAction.setAsDefault,
    false => DefaultWatchlistAction.removeDefault,
  };

  Future getWatchlistDetail() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final symbols = state.watchlist.symbols;

      final data = await Future.wait([
        stockCommonRepository.getAllStock(loadCache: true),
        watchlistRepository.getWatchListDefault(),
      ]);

      final allStock = data.firstOrNull as List<StockModel>?;
      final watchlist = data.lastOrNull as WatchlistModel?;

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          isDefaultWatchlist: watchlist?.id == state.watchlist.id ? true : null,
          stocks: allStock?.where((e) => symbols.contains(e.symbol)).toList(),
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future saveWatchlistDefault() async {
    try {
      emit(state.copyWith(updateWatchlistStatus: ApiStatus.loading()));

      await watchlistRepository.updateWatchlistDefault(
        watchlist: state.watchlist,
        action: watchlistAction,
      );

      emit(state.copyWith(updateWatchlistStatus: ApiStatus.done()));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(updateWatchlistStatus: ApiStatus.error(e)));
    }
  }

  Future updateWatchList() async {
    try {
      emit(state.copyWith(updateWatchlistStatus: ApiStatus.loading()));

      final symbols = state.stocks?.map((e) => e.symbol).toList();

      final newWatchList = await watchlistRepository.updateWatchList(
        symbols: symbols ?? state.watchlist.symbols,
        id: state.watchlist.id,
        name: state.watchlistName,
        action: watchlistAction,
      );

      emit(
        state.copyWith(
          watchlist: newWatchList,
          updateWatchlistStatus: ApiStatus.done(),
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(updateWatchlistStatus: ApiStatus.error(e)));
    }
  }

  Future deleteWatchlist() async {
    try {
      emit(state.copyWith(deleteWatchlistStatus: ApiStatus.loading()));

      await watchlistRepository.deleteWatchlist(id: state.watchlist.id);

      emit(state.copyWith(deleteWatchlistStatus: ApiStatus.done()));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(deleteWatchlistStatus: ApiStatus.error(e)));
    }
  }

  void onRemoveStock(StockModel stock) {
    final newData = [...?state.stocks]..remove(stock);

    emit(state.copyWith(stocks: newData));
  }

  void onReorderChanged(int oldIndex, int newIndex) {
    final newData = [...?state.stocks];
    final oldWatchlist = newData[oldIndex];
    newData[oldIndex] = newData[newIndex];
    newData[newIndex] = oldWatchlist;

    emit(state.copyWith(stocks: newData));
  }

  void onNameChanged(String name) {
    emit(state.copyWith(watchlistName: name.trim()));
  }

  void onDefaultChanged(bool isDefault) {
    emit(state.copyWith(isDefaultWatchlist: isDefault));
  }
}
