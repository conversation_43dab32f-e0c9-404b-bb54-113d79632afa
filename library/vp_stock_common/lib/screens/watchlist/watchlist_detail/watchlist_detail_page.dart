import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

part 'widgets/stock_list_in_watchlist.dart';

part 'widgets/watchlist_item_view.dart';

part 'widgets/bottom_action_view.dart';

part 'widgets/input_view.dart';

part 'widgets/switch_view.dart';

class WatchlistDetailBottomSheet extends StatelessWidget {
  const WatchlistDetailBottomSheet({required this.watchlist, super.key});

  final WatchlistModel watchlist;

  @override
  Widget build(BuildContext context) {
    return ScaffoldBottomSheet(
      child: <PERSON><PERSON><PERSON><PERSON>(
        create:
            (_) =>
                WatchlistDetailBloc(watchlist: watchlist)..getWatchlistDetail(),
        child: MultiBlocListener(
          listeners: [
            BlocListener<WatchlistDetailBloc, WatchlistDetailState>(
              listenWhen: (_, state) => state.apiStatus.isError,
              listener: (context, _) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<WatchlistDetailBloc, WatchlistDetailState>(
              listenWhen: (_, state) => state.updateWatchlistStatus.isError,
              listener: (context, _) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<WatchlistDetailBloc, WatchlistDetailState>(
              listenWhen: (_, state) => state.deleteWatchlistStatus.isError,
              listener: (context, _) {
                context.showError(content: 'Đã có lỗi xảy ra');
              },
            ),
            BlocListener<WatchlistDetailBloc, WatchlistDetailState>(
              listenWhen: (_, state) => state.updateWatchlistStatus.isDone,
              listener: (context, state) {
                onUpdateSuccess(context, state.watchlist);
              },
            ),
            BlocListener<WatchlistDetailBloc, WatchlistDetailState>(
              listenWhen: (_, state) => state.deleteWatchlistStatus.isDone,
              listener: (context, state) {
                onDeleteSuccess(context, state.watchlist);
              },
            ),
          ],
          child: BlocBuilder<WatchlistDetailBloc, WatchlistDetailState>(
            buildWhen: (pState, state) => pState.apiStatus != state.apiStatus,
            builder: (_, state) {
              return VPLoadingBuilder(
                showLoading: state.apiStatus.isLoading,
                builder: (_, child) => child!,
                child: const _WatchlistDetailBody(),
              );
            },
          ),
        ),
      ),
    );
  }

  void onUpdateSuccess(BuildContext context, WatchlistModel watchlist) {
    context.read<WatchlistBloc>().update(watchlist);

    context.showSuccess(content: 'Lưu thành công');

    Navigator.pop(context);
  }

  void onDeleteSuccess(BuildContext context, WatchlistModel watchlist) {
    context.read<WatchlistBloc>().remove(watchlist);

    context.showSuccess(content: 'Đã xóa danh sách');

    Navigator.pop(context);
  }
}

class _WatchlistDetailBody extends StatelessWidget {
  const _WatchlistDetailBody();

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tên danh sách',
            style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
          ),

          const SizedBox(height: 16),

          const _InputView(),

          const SizedBox(height: 16),

          const Flexible(child: _StockListInWatchlistView()),

          const VPDividerView(),

          const SizedBox(height: 16),

          const _SwitchView(),

          const SizedBox(height: 16),

          const _BottomActionView(),
        ],
      ),
    );
  }
}
