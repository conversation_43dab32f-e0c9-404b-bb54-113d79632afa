import 'package:flutter/material.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/core/repository/watchlist_repository.dart';
import 'package:vp_stock_common/core/service/chart_service.dart';
import 'package:vp_stock_common/core/service/stock_portfolio_service.dart';
import 'package:vp_stock_common/core/service/watchlist_service.dart';
import 'package:vp_stock_common/hive_registrar.g.dart';
import 'package:vp_stock_common/screens/search/bond_search/bond_search_page.dart';
import 'package:vp_stock_common/screens/search/stock_search/search_page.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

class VpStockCommonModule implements Module {
  @override
  void injectServices(GetIt service) {
    Hive.registerAdapters();

    service.registerFactory<StockCommonNavigator>(
      () => StockCommonNavigatorImpl(),
    );

    service.registerLazySingleton(() => StockCommonService(service()));
    service.registerLazySingleton<StockCommonRepository>(
      () => StockCommonRepositoryImpl(
        stockCommonService: service(),
        localDataService: service(),
      ),
    );

    service.registerLazySingleton(() => StockPortfolioService(service()));
    service.registerLazySingleton<StockPortfolioRepository>(
      () => StockPortfolioRepositoryImpl(service()),
    );

    service.registerLazySingleton(() => WatchListService(service()));
    service.registerFactory<WatchlistRepository>(
      () => WatchListRepositoryImpl(
        watchListService: service(),
        localDataService: service(),
        stockPortfolioService: service(),
        stockCommonService: service(),
      ),
    );

    service.registerLazySingleton(() => ChartService(service()));
    service.registerLazySingleton<ChartRepository>(
      () => ChartRepositoryImpl(chartService: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        name: VPStockCommonRouter.search.routeName,
        path: VPStockCommonRouter.search.routeName,
        builder: (context, state) {
          return SearchPage(
            args: SearchArgsExtensions.fromQueryParams(
              state.uri.queryParameters,
            ),
            extraArgs: state.extra as SearchExtraArgs?,
          );
        },
      ),
      GoRoute(
        path: VPStockCommonRouter.bondSearch.routeName,
        builder: (context, state) {
          return const BondSearchPage();
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [];
  }

  @override
  String modulePath() {
    return "vpStockCommon";
  }
}
