import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:new_neo_invest/cuong_bloc.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:stock_detail/stock_detail_module.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_auth/vp_auth_module.dart';
import 'package:vp_centralize/vp_centralize.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_finvest/vp_finvest.dart';
import 'package:vp_loyalty/vp_loyalty.dart';
import 'package:vp_money/vp_money_module.dart';
import 'package:vp_partner_connection/vp_partner_connection_module.dart';
import 'package:vp_portfolio/vp_portfolio.dart';
import 'package:vp_price_board/vp_price_board_module.dart';
import 'package:vp_settings/vp_settings_module.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/vp_trading.dart';
import 'package:vp_utility/vp_utility.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final modules = <Module>[
    VpStockCommonModule(),
    MainAppModule(),
    AuthModule(),
    VpPriceBoardModule(),
    AssetsModule(),
    TradingModule(),
    LoyaltyModule(),
    VPStockDetailModule(),
    VpUtilityModule(),
    VpMoneyModule(),
    SettingsModule(),
    VpCentralizeModule(),
    VpPortfolioModule(),
    VpFinvestModule(),
    PartnerConnectionModule(),
  ];

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  ModuleManagement().addModules(modules);
  await ModuleManagement().injectDependencies();

  Bloc.observer = CuongAppBlocObserver();
  runApp(MyApp());
}

class RouteTracker {
  static String lastRoute = MainRouter.splash.routeName;
}

class MyApp extends StatelessWidget {
  MyApp({super.key}) {
    ModuleManagement()
        .getModules()
        .forEach((module) => routes.addAll(module.router()));
  }

  final List<RouteBase> routes = [];

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: GetIt.instance<ThemeCubit>()),
        BlocProvider.value(value: GetIt.instance<AuthCubit>()),
        BlocProvider(create: (_) => WatchlistBloc()),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AuthCubit, AuthState>(
            listenWhen: (preState, state) => preState.status != state.status,
            listener: (context, state) {
              if (state.status == AuthStatus.logined) {
                router.go(MainRouter.main.routeName);
              }
            },
          ),
          BlocListener<AuthCubit, AuthState>(
            listenWhen: (preState, state) => preState.status != state.status,
            listener: (context, state) {
              if (state.status == AuthStatus.nologin) {
                context.read<WatchlistBloc>().clear();
              }
            },
          ),
        ],
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (BuildContext ctx, state) {
            return BlocBuilder<AuthCubit, AuthState>(
              builder: (BuildContext ctx, state) {
                return LogApiConfigScreen(
                  navigatorKey:
                      GetIt.instance<NavigationService>().navigatorKey,
                  child: MaterialApp.router(
                    title: 'VPBANKS',
                    color: Colors.white,
                    theme: ctx.read<ThemeCubit>().themeData,
                    debugShowCheckedModeBanner: false,
                    locale: const Locale('vi', ''),
                    supportedLocales: const [
                      Locale('en', ''),
                      Locale('vi', ''),
                    ],
                    localizationsDelegates:
                        ModuleManagement().localizationsDelegates(),
                    builder: vpToastBuilder(),
                    routerConfig: router,
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}

final router = GoRouter(
  navigatorKey: GetIt.instance<NavigationService>().navigatorKey,
  debugLogDiagnostics: true,
  redirect: (context, state) async {
    final path = state.uri.path;
    RouteTracker.lastRoute = path;

    if (path == '/') {
      return MainRouter.splash.routeName;
    }
    return state.uri.toString();
  },
  initialLocation: RouteTracker.lastRoute,
  routes: ModuleManagement().onGenerateRoute(),
);
