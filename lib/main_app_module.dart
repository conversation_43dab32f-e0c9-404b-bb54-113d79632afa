import 'package:flutter/widgets.dart';
import 'package:multiple_localization/multiple_localization.dart';
import 'package:new_neo_invest/core/repository/home_repository.dart';
import 'package:new_neo_invest/core/repository/notification_repository.dart';
import 'package:new_neo_invest/core/repository/personal_photo_repository.dart';
import 'package:new_neo_invest/core/service/home_service.dart';
import 'package:new_neo_invest/core/service/notification_service.dart';
import 'package:new_neo_invest/core/service/personal_photo_service.dart';
import 'package:new_neo_invest/generated/intl/messages_all.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/main_navigator.dart';
import 'package:new_neo_invest/screen/feature/all_feature_screen.dart';
import 'package:new_neo_invest/screen/main_tabbar/main_tabbar_screen.dart';
import 'package:new_neo_invest/screen/splash/splash_screen.dart';
import 'package:new_neo_invest/screen/stock_home/stock_home_screen.dart';
import 'package:new_neo_invest/screen/welcome/welcome_screen.dart';
import 'package:vp_auth/core/constant/account_path_api.dart';
import 'package:vp_common/vp_common.dart' show VPSocketInvestConnect;
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

enum MainRouter {
  priceBoard('/priceBoard'),
  splash('/splash'),
  welcome('/welcome'),
  main('/mainTabbar'),
  mainStockHome('/mainStockHome'),
  allFeature('/all_feature'),
  supperCombo('/supper_combo');

  const MainRouter(this.routeName);

  final String routeName;
}

class MainAppModule implements Module {
  @override
  void injectServices(GetIt service) {
    service
      ..registerLazySingleton(() => NotificationService(service()))
      ..registerLazySingleton(() => HomeService(service()))
      ..registerLazySingleton(() => PersonalPhotoService(service()))
      ..registerLazySingleton<NotificationRepository>(
        () => NotificationRepositoryImpl(notificationService: service()),
      )
      ..registerLazySingleton<HomeRepository>(
        () => HomeRepositoryImpl(homeService: service()),
      )
      ..registerLazySingleton<PersonalPhotoRepository>(
        () => PersonalPhotoRepositoryImpl(personalPhotoService: service()),
      )
      ..registerLazySingleton<MainNavigator>(
        MainNavigatorImpl.new,
      );

    VPSocketInvestConnect.instance.connect();
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: MainRouter.splash.routeName,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: MainRouter.welcome.routeName,
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: MainRouter.main.routeName,
        builder: (context, state) => const MainTabbarScreen(),
      ),
      GoRoute(
        name: MainRouter.mainStockHome.routeName,
        path: MainRouter.mainStockHome.routeName,
        builder: (context, state) {
          final args = StockHomeArgsExtensions.fromQueryParams(
            state.uri.queryParameters,
          );
          return StockHomeScreen(args: args);
        },
      ),
      GoRoute(
        path: MainRouter.allFeature.routeName,
        builder: (context, state) => const AllFeatureScreen(),
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [_MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return vpAuth;
  }
}

class _MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const _MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = _MultiLocalizationsDelegate();

  @override
  Future<VPNeoLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPNeoLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
