import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:new_neo_invest/cubit/stock_home/stock_home_cubit.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/screen/stock_home/widget/stock_bottom_widget.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/vp_price_board.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/screen/holding_portfolio/holding_portfolio_screen.dart';
import 'package:vp_trading/vp_trading.dart';

class StockHomeScreen extends StatefulWidget {
  const StockHomeScreen({required this.args, super.key});

  final StockHomeArgs args;

  @override
  State<StockHomeScreen> createState() => _StockHomeScreenState();
}

class _StockHomeScreenState extends State<StockHomeScreen> {
  int get initialPage => widget.args.initialPage;

  late final PageController _pageController =
      PageController(initialPage: initialPage);

  final _children = <Widget>[
    const PriceBoardPage(),
    const HoldingPortfolioScreen(),
    const OrderContainerScreen(),
    const SizedBox(),
    // const CommandHistoryView(),
    // const UtilitiesView(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => StockHomeCubit(initialPage: initialPage),
      child: AppScaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingButton: FloatingActionButton(
          onPressed: () {
            context.push(
              '/placeOrder',
            );
          },
          backgroundColor: themeData.primary,
          child: SvgPicture.asset(Assets.icons.icSetCommand),
        ),
        resizeToAvoidBottomInset: false,
        bottomBar: const StockManagerBottomView(),
        child: Stack(
          children: [
            BlocConsumer<StockHomeCubit, StockHomeState>(
              listener: (context, state) {
                _pageController.jumpToPage(state.page);
              },
              builder: (context, state) {
                return PageView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  onPageChanged: (index) {
                    context.read<StockHomeCubit>().changePage(index);
                  },
                  children: _children,
                );
              },
            ),
            const Align(alignment: Alignment.bottomCenter, child: EODView()),
          ],
        ),
      ),
    );
  }
}
