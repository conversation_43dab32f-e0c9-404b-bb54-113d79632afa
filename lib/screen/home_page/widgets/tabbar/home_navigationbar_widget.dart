import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:new_neo_invest/screen/home_page/widgets/tabbar/invest_navigate.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/stock_home_args.dart';

class HomeNavigationBar extends StatelessWidget with HomeNavigator {
  const HomeNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    final autoSizeGroup = AutoSizeGroup();
    return BottomAppBar(
      color: vpColor.backgroundElevation1,
      notchMargin: 8,
      shape: const CircularNotchedRectangle(),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: HomeNavItem(
              icon: Assets.icons.icSec,
              title: VPNeoLocalize.current.home_sec,
              onTap: () {
                navigateToStockHome(context: context);
              },
              autoSizeGroup: autoSizeGroup,
            ),
          ),
          Expanded(
            child: HomeNavItem(
              icon: Assets.icons.icChangeMoney,
              title: VPNeoLocalize.current.home_derivatives,
              onTap: navigateToDerivative,
              autoSizeGroup: autoSizeGroup,
            ),
          ),
          // Expanded(
          //   child: NavItem(
          //     pathIcon: HomeRes.icDerivatives,
          //     title: getHomeLang(HomeLangKey.navigateDerivatives),
          //     onTap: () {},
          //     autoSizeGroup: autoSizeGroup,
          //     sizeIcon: 24,
          //   ),
          // ),
          Expanded(
            child: HomeNavItem(
              icon: Assets.icons.icInvest,
              title: VPNeoLocalize.current.home_invest,
              onTap: () => openInvestNavigate(context),
              autoSizeGroup: autoSizeGroup,
            ),
          ),
          // Expanded(
          //   child: NavItem(
          //     pathIcon: HomeRes.icCommunity,
          //     title: getHomeLang(HomeLangKey.navigateCommunity),
          //     onTap: () {},
          //     autoSizeGroup: autoSizeGroup,
          //     sizeIcon: 24,
          //   ),
          // ),
          Expanded(
            child: HomeNavItem(
              icon: Assets.icons.icAsset,
              title: VPNeoLocalize.current.home_asset,
              onTap: () {
                navigateToMoney(context: context);
              },
              autoSizeGroup: autoSizeGroup,
            ),
          )
        ],
      ),
    );
  }

  void openInvestNavigate(BuildContext context) {
    showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return const InvestNavigate();
      },
    );
  }
}

mixin HomeNavigator {
  void navigateToStockHome({
    required BuildContext context,
    StockHomeArgs? args,
  }) {
    context.pushNamed(
      MainRouter.mainStockHome.routeName,
      queryParameters: args?.toQueryParams() ?? const {},
    );
  }

  void navigateToDerivative({int? initialPage}) {
    // DerivativeModuleConfig().initConfig(
    //     sl.get<StockBuildConfigs>().socketDerivativeUrl(),
    //     sl.get<StockBuildConfigs>().socketDerivativeUrlNew());
    // navigation.navigateTo(DerivativeRouterManager.derivativeHome,
    //     arguments: initialPage ?? 0);
  }

  void navigateToMarket() {
    //navigation.navigateTo(MarketRouter.marketRouter);
  }

  void navigateToBondHome() {
    // bondNavigation.navigateTo(ListedBondRouter.bondManagerPage);
  }

  void navigateToMoney({required BuildContext context, int initialPage = 0}) {
    context.push(
      AssetRouter.assetOverview.routeName,
      extra: AssetOverviewPageArgument(initialPage: initialPage),
    );
  }

// navigateToStockDetail(StockDetailEntity entity) {
//   // navigation.navigateTo(
//   //   DetailsStockRouter.detailsStockRouter2,
//   //   arguments: {'symbol': entity.symbol, 'closePrice': entity.price},
//   // );
// }
}

class HomeNavItem extends StatelessWidget {
  const HomeNavItem(
      {Key? key,
      required this.icon,
      required this.title,
      required this.onTap,
      required this.autoSizeGroup})
      : super(key: key);

  final String icon;
  final String title;
  final VoidCallback onTap;
  final AutoSizeGroup autoSizeGroup;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          SvgPicture.asset(
            icon,
            width: 24,
            height: 24,
          ),
          const SizedBox(
            height: 8,
          ),
          AutoSizeText(
            title,
            style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
            maxLines: 1,
            minFontSize: 5,
            textAlign: TextAlign.center,
            group: autoSizeGroup,
          ),
        ],
      ),
    );
  }
}
